import { Button } from '@/components/base/inputs/button';
import TableRows from '@mui/icons-material/TableRows';
import ViewHeadline from '@mui/icons-material/ViewHeadline';
import ViewStream from '@mui/icons-material/ViewStream';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import {
  gridDensitySelector,
  ToolbarButton,
  useGridApiContext,
  useGridSelector,
  type GridDensity,
} from '@mui/x-data-grid-premium';
import { useRef, useState, type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Configuration options for the DataGrid row density settings.
 */
const DENISTY_OPTIONS: { label: string; value: GridDensity; icon: ReactNode }[] = [
  { label: 'common.label.compact', value: 'compact', icon: <ViewHeadline /> },
  { label: 'common.label.standard', value: 'standard', icon: <TableRows /> },
  { label: 'common.label.comfortable', value: 'comfortable', icon: <ViewStream /> },
];

/**
 * CustomDensityButton component provides row density control for DataGrid
 * with options to switch between compact, standard, and comfortable views
 */
const CustomDensityButton = () => {
  const { t } = useTranslation();

  const apiRef = useGridApiContext();
  const density = useGridSelector(apiRef, gridDensitySelector);
  const [densityMenuOpen, setDensityMenuOpen] = useState(false);
  const densityMenuTriggerRef = useRef<HTMLButtonElement>(null);
  // Dynamic icon based on current density
  const getCurrentDensityIcon = () => {
    const currentOption = DENISTY_OPTIONS.find((option) => option.value === density);
    return currentOption?.icon || <TableRows />;
  };

  // In render:

  return (
    <>
      <ToolbarButton
        ref={densityMenuTriggerRef}
        id="density-menu-trigger"
        aria-controls="density-menu"
        aria-haspopup="true"
        aria-expanded={densityMenuOpen ? 'true' : undefined}
        onClick={() => setDensityMenuOpen(true)}
        render={
          <Button
            arrow={true}
            color="primary"
            title={t('common.label.selectDensity')}
            size="small"
            tooltipProps={{
              placement: 'bottom',
            }}
            variant="text"
            // startIcon={<TableRows />}
            startIcon={getCurrentDensityIcon()}
          >
            {t('common.button.density')}
          </Button>
        }
      />
      <Menu
        id="density-menu"
        anchorEl={densityMenuTriggerRef.current}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        open={densityMenuOpen}
        onClose={() => setDensityMenuOpen(false)}
        slotProps={{ list: { 'aria-labelledby': 'density-menu-trigger' } }}
      >
        {DENISTY_OPTIONS.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => {
              apiRef.current.setDensity(option.value);
              setDensityMenuOpen(false);
            }}
            selected={density === option.value}
          >
            <ListItemIcon>{option.icon}</ListItemIcon>
            <ListItemText>{t(option.label)}</ListItemText>
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default CustomDensityButton;
