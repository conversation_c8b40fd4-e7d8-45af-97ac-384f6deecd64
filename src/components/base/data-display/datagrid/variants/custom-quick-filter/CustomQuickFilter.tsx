import {
  StyledQuickFilter,
  StyledTextField,
} from '@/components/base/data-display/datagrid/styles/QuickFilter.style';
import Cancel from '@mui/icons-material/Cancel';
import Search from '@mui/icons-material/Search';
import InputAdornment from '@mui/material/InputAdornment';
import {
  QuickFilterClear,
  QuickFilterControl,
  type GridToolbarQuickFilterProps,
} from '@mui/x-data-grid-premium';

/**
 * CustomQuickFilter component provides a search functionality for DataGrid
 * with expandable search input and clear button
 */
const CustomQuickFilter = ({
  quickFilterProps,
}: {
  quickFilterProps: GridToolbarQuickFilterProps;
}) => {
  return (
    <StyledQuickFilter
      expanded
      {...quickFilterProps}
    >
      <QuickFilterControl
        render={({ ref, ...controlProps }, state) => (
          <StyledTextField
            {...controlProps}
            ownerState={{ expanded: state.expanded }}
            inputRef={ref}
            aria-label="Search"
            size="small"
            slotProps={{
              input: {
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
                endAdornment: state.value ? (
                  <InputAdornment position="end">
                    <QuickFilterClear
                      edge="end"
                      size="small"
                      aria-label="Clear search"
                      material={{ sx: { marginRight: -0.75 } }}
                    >
                      <Cancel fontSize="small" />
                    </QuickFilterClear>
                  </InputAdornment>
                ) : null,
                ...controlProps.slotProps?.input,
              },
              ...controlProps.slotProps,
            }}
          />
        )}
      />
    </StyledQuickFilter>
  );
};

export default CustomQuickFilter;
