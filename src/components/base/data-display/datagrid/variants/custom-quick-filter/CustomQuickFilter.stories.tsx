import { getByXPath } from '@/utils/xpath-selector.util';
import { DataGridPremium, Toolbar, type GridPremiumSlotsComponent } from '@mui/x-data-grid-premium';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, waitFor, within } from 'storybook/test';
import CustomQuickFilter from './CustomQuickFilter';

// Sample data for testing
const generateSampleData = (count: number = 50) => {
  const names = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
  const statuses = ['Active', 'Inactive', 'Pending', 'Suspended'];

  return Array.from({ length: count }, (_, index) => {
    const id = index + 1;
    const nameIndex = Math.floor(Math.random() * names.length);
    const statusIndex = Math.floor(Math.random() * statuses.length);

    return {
      id,
      name: names[nameIndex],
      status: statuses[statusIndex],
      email: `${names[nameIndex]!.toLowerCase().replace(' ', '.')}${id}@example.com`,
      department: ['HR', 'IT', 'Finance', 'Marketing'][Math.floor(Math.random() * 4)],
    };
  });
};

const sampleRows = generateSampleData();
const sampleColumns = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'name', headerName: 'Name', width: 150 },
  { field: 'email', headerName: 'Email', width: 220 },
  { field: 'status', headerName: 'Status', width: 120 },
  { field: 'department', headerName: 'Department', width: 150 },
];

/**
 * Storybook metadata configuration for the CustomQuickFilter component.
 */
const meta = {
  component: CustomQuickFilter,
  title: 'Components/base/data-display/datagrid/CustomQuickFilter',
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component: `
CustomQuickFilter provides an expandable search functionality for DataGrid with smooth animations and clear functionality.

## Features
- Expandable search input with smooth transitions
- Search icon trigger button with tooltip
- Clear button when text is entered
- Debounced search for performance
- Internationalization support
- Accessibility features

## Usage
This component is typically used within a DataGrid toolbar to provide quick filtering capabilities.
        `,
      },
    },
  },
  argTypes: {
    quickFilterProps: {
      description: 'Configuration props for the quick filter functionality',
      control: { type: 'object' },
      table: {
        type: { summary: 'GridToolbarQuickFilterProps' },
        defaultValue: { summary: '{ debounceMs: 500 }' },
      },
    },
  },
  decorators: [
    (Story, context) => {
      const CustomToolbarComponent = () => (
        <Toolbar>
          <Story />
        </Toolbar>
      );

      const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
        toolbar: CustomToolbarComponent,
      };

      // Use sample data for better testing experience
      const useRealData = context.parameters?.useRealData ?? true;

      return (
        <DataGridPremium
          rows={useRealData ? sampleRows : []}
          columns={useRealData ? sampleColumns : [{ field: 'id', headerName: 'ID' }]}
          showToolbar
          slots={defaultSlotProps}
          initialState={{
            pagination: { paginationModel: { pageSize: 10 } },
          }}
          pageSizeOptions={[10, 25, 50]}
        />
      );
    },
  ],
} satisfies Meta<typeof CustomQuickFilter>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default story showing the CustomQuickFilter with standard configuration.
 */
export const Default: Story = {
  args: {
    quickFilterProps: {
      debounceMs: 500,
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          'The default CustomQuickFilter with 500ms debounce and sample data for testing search functionality.',
      },
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Should show search input field', async () => {
      const searchInput = canvas.getByPlaceholderText('common.label.searchKeywords');
      expect(searchInput).toBeInTheDocument();
      expect(searchInput).toHaveAttribute('aria-label', 'Search');
    });

    await step('Should show tooltip on search trigger button', async () => {
      const searchButton = canvas.getByRole('button', { name: /search/i });
      await userEvent.hover(searchButton);

      const tooltip = await waitFor(() =>
        getByXPath(`//div[@role="tooltip" and contains(@class, "MuiTooltip-popper")]`, document)
      );
      expect(tooltip).toBeInTheDocument();
    });

    await step('Should show clear button when text is entered', async () => {
      const searchInput = canvas.getByRole('searchbox');
      await userEvent.type(searchInput, 'Alice');

      const clearButton = await waitFor(() => canvas.getByTestId('CancelIcon'));
      expect(clearButton).toBeInTheDocument();
    });

    await step('Should filter results when searching', async () => {
      // Wait for debounce and check if filtering occurred
      await waitFor(
        () => {
          const gridContainer = within(canvas.getByRole('grid'));
          // Should have fewer rows after filtering
          expect(gridContainer.getAllByRole('row').length).toBeLessThan(51); // 50 data rows + header
        },
        { timeout: 1000 }
      );
    });

    await step('Should clear search with clear button', async () => {
      const clearButton = canvas.getByTestId('CancelIcon');
      await userEvent.click(clearButton);

      await waitFor(() => {
        expect(canvas.queryByTestId('CancelIcon')).not.toBeInTheDocument();
      });

      const searchInput = canvas.getByRole('searchbox');
      expect(searchInput).toHaveValue('');
    });

    await step('Should clear search with Escape key', async () => {
      const searchInput = canvas.getByRole('searchbox');
      await userEvent.type(searchInput, 'Test');
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(canvas.queryByTestId('CancelIcon')).not.toBeInTheDocument();
        expect(searchInput).toHaveValue('');
      });
    });
  },
};

/**
 * Story showing CustomQuickFilter with fast debounce for immediate search.
 */
export const FastDebounce: Story = {
  args: {
    quickFilterProps: {
      debounceMs: 100,
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'CustomQuickFilter with 100ms debounce for near-instant search results.',
      },
    },
  },
};

/**
 * Story showing CustomQuickFilter with slow debounce for reduced API calls.
 */
export const SlowDebounce: Story = {
  args: {
    quickFilterProps: {
      debounceMs: 1000,
    },
  },
  parameters: {
    docs: {
      description: {
        story: 'CustomQuickFilter with 1000ms debounce to reduce server requests.',
      },
    },
  },
};

/**
 * Story showing CustomQuickFilter with minimal data for testing edge cases.
 */
export const MinimalData: Story = {
  args: {
    quickFilterProps: {
      debounceMs: 500,
    },
  },
  parameters: {
    useRealData: false,
    docs: {
      description: {
        story: 'CustomQuickFilter with minimal data to test behavior with empty or small datasets.',
      },
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Should handle empty dataset gracefully', async () => {
      const searchInput = canvas.getByRole('searchbox');
      await userEvent.type(searchInput, 'No results');

      // Should not crash with empty data
      expect(searchInput).toHaveValue('No results');
    });
  },
};

/**
 * Story demonstrating accessibility features of CustomQuickFilter.
 */
export const AccessibilityDemo: Story = {
  args: {
    quickFilterProps: {
      debounceMs: 500,
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          'Demonstrates accessibility features including ARIA labels, keyboard navigation, and screen reader support.',
      },
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Should have proper ARIA attributes', async () => {
      const searchInput = canvas.getByRole('searchbox');
      expect(searchInput).toHaveAttribute('aria-label', 'Search');
      expect(searchInput).toHaveAttribute('placeholder', 'Search keywords');
    });

    await step('Should support keyboard navigation', async () => {
      const searchInput = canvas.getByRole('searchbox');

      // Focus with Tab
      await userEvent.tab();
      expect(searchInput).toHaveFocus();

      // Type and clear with keyboard
      await userEvent.type(searchInput, 'Test search');
      expect(searchInput).toHaveValue('Test search');

      // Clear with Escape
      await userEvent.keyboard('{Escape}');
      expect(searchInput).toHaveValue('');
    });

    await step('Should have accessible clear button', async () => {
      const searchInput = canvas.getByRole('searchbox');
      await userEvent.type(searchInput, 'Test');

      const clearButton = await waitFor(() => canvas.getByTestId('CancelIcon'));
      expect(clearButton.closest('button')).toHaveAttribute('aria-label', 'Clear search');
    });
  },
};
