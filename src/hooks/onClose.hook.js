import fp from 'fastify-plugin';

/**
 * onClose hook
 *
 * This hook is executed when the server is closing.
 * It can be used for cleanup operations or closing database connections.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onCloseHook = (fastify, options) => {
  fastify.addHook('onClose', async (instance) => {
    try {
      // Perform any cleanup tasks here
      // Example: Close database connections, clear queues, etc.

      // Close all worker processes
      const workers = fastify.workers;
      for (const worker of Object.values(workers)) {
        worker.close();
      }
    } catch (error) {
      fastify.log.error(error, 'Error during shutdown');
      throw error;
    }
  });
};

export default fp(onCloseHook, {
  name: 'onCloseHook',
});
