import fp from 'fastify-plugin';

import { pinoLogger } from '#config/pino-logger.config.js';

/**
 * A Fastify plugin for logging requests, responses, and errors.
 * It decorates the Fastify instance with a 'log' property, which is a Pino logger instance.
 * The plugin also adds various lifecycle hooks to log relevant information.
 *
 * @param {FastifyInstance} fastify - The Fastify instance to be decorated.
 * @param {FastifyPluginOptions} opts - Additional options for the plugin.
 * @returns {Promise<void>} - A promise that resolves when the plugin is initialized.
 */
const loggerPlugin = async (fastify, opts) => {
  fastify.decorate('log', pinoLogger);
};

export default fp(loggerPlugin, { name: 'logger' });
