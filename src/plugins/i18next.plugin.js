import Backend from 'i18next-http-backend';
import fp from 'fastify-plugin';
import i18next from 'i18next';

import {
  createI18nextOptions,
  dateFormatOptions,
  numberFormatOptions,
} from '#config/i18next.config.js';

/**
 * Initializes and configures the i18next plugin for Fastify.
 * This plugin sets up internationalization support, including backend configuration,
 * language detection, and automatic translation of response messages.
 *
 * @async
 * @param {Object} fastify - The Fastify instance to which the plugin is being added.
 * @param {Object} options - Configuration options for the plugin (currently unused).
 * @returns {void}
 */
const i18nextPlugin = async (fastify, options) => {
  const i18nextOptions = createI18nextOptions(fastify);
  i18next.use(Backend).init(i18nextOptions);

  const namespaces = i18nextOptions.ns;
  fastify.decorate('t', (key, options) => i18next.t(key, options));

  namespaces.forEach((ns) => {
    fastify.decorate(`t_${ns}`, (key, options) => i18next.t(key, { ...options, ns }));
  });

  fastify.decorateRequest('locale', null);

  fastify.decorate('formatDate', (date, options) => {
    const defaultOptions = dateFormatOptions;
    const mergedOptions = { ...defaultOptions, ...options };
    return new Intl.DateTimeFormat(fastify.locale, mergedOptions).format(date);
  });

  fastify.decorate('formatNumber', (number, options, currencyCode = null) => {
    const defaultOptions = numberFormatOptions;

    if (currencyCode) {
      defaultOptions.style = 'currency';
      defaultOptions.currency = currencyCode;
      defaultOptions.minimumFractionDigits = 2;
    }
    const mergedOptions = { ...defaultOptions, ...options };
    return new Intl.NumberFormat(fastify.locale, mergedOptions).format(number);
  });
};

export default fp(i18nextPlugin, {
  name: 'i18next',
});
