import fastifyJwt from '@fastify/jwt';
import fp from 'fastify-plugin';

const jwtPlugin = async (fastify, opts) => {
  const jwtAlgorithm = process.env.JWT_ALGORITHM;
  const jwtAudience = process.env.JWT_AUDIENCE;
  const jwtIssuer = process.env.JWT_ISSUER;
  const jwtSecret = process.env.JWT_SECRET;

  fastify.register(fastifyJwt, {
    secret: jwtSecret,
    sign: {
      algorithm: jwtAlgorithm,
      aud: jwtAudience,
      iss: jwtIssuer,
    },
    verify: {
      allowedAud: jwtAudience,
      allowedIss: jwtIssuer,
    },
  });
  fastify.log.info('JWT plugin registered successfully');
};

export default fp(jwtPlugin, { name: 'jwt' });
