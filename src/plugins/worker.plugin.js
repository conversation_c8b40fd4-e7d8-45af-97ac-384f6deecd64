import IORedis from 'ioredis';
import { Worker } from 'bullmq';
import fp from 'fastify-plugin';

import * as Workers from '#src/modules/core/workers/index.js';
import { baseConfig, workerConcurrency } from '#config/queue.config.js';

/**
 * Initializes and registers BullMQ workers for processing background jobs.
 * This plugin sets up workers for different queues to handle asynchronous tasks.
 *
 * @async
 * @function workersPlugin
 * @param {Object} fastify - The Fastify instance to which the workers will be attached.
 * @param {Object} opts - Plugin options (not currently used).
 * @returns {void} - This function doesn't return a value but decorates the Fastify instance with worker objects.
 */
const workersPlugin = async (fastify, opts) => {
  const connection = new IORedis({ ...baseConfig, maxRetriesPerRequest: null });

  // const generalWorker = new Worker(
  //   'general-queue',
  //   async (job) => {
  //     switch (job.name) {
  //       case 'general-job':
  //       // return
  //       case 'general-job2':
  //       // return
  //     }
  //   },
  //   {
  //     connection: connection,
  //     concurrency: workerConcurrency,
  //   },
  // );

  const entityWorker = new Worker(
    'entity-queue',
    async (job) => {
      switch (job.name) {
        case 'create':
          return Workers.EntityWorker.createMerchantJob(fastify, job.data);
        case 'check-balance':
          return Workers.EntityWorker.checkCreditLimitThreshold(fastify, job.data);
      }
    },
    {
      connection: connection,
      concurrency: workerConcurrency,
    },
  );

  fastify.decorate('workers', {
    // generalWorker,
    entityWorker,
  });
};

export default fp(workersPlugin, { name: 'workers' });
