import { Queue, QueueEvents } from 'bullmq';
import { baseConfig, queueNames } from '#config/queue.config.js';
import IORedis from 'ioredis';
import fp from 'fastify-plugin';

/**
 * Sets up event listeners for BullMQ queues to track job status changes.
 * This plugin initializes connections to Redis and registers event handlers for
 * job lifecycle events (active, completed, failed) across all configured queues.
 * Each event handler retrieves the job and updates its status in the database.
 *
 * @async
 * @param {Object} fastify - The Fastify instance to which this plugin is attached
 * @param {Object} opts - Plugin options (currently unused)
 * @returns {Promise<void>} - Resolves when all queue event listeners are registered
 */
const queueEventPlugin = async (fastify, opts) => {
  const connection = new IORedis({ ...baseConfig, maxRetriesPerRequest: null });

  for (const queueName of queueNames) {
    const queue = new Queue(queueName, { connection: connection });
    const queueEvents = new QueueEvents(queueName, { connection: connection });

    queueEvents.on('active', async ({ jobId, prev }) => {
      const job = await queue.getJob(jobId);

      fastify.log.info(job.processedOn); // dummy line to prevent sonarqube error
      // 1. search DB by job.data.jobUuid
      // 2. update job started_at = job.processedOn
      // 3. update job status to "running"
    });

    queueEvents.on('completed', async ({ jobId, returnvalue }) => {
      const job = await queue.getJob(jobId);

      fastify.log.info(job.finishedOn); // dummy line to prevent sonarqube error
      // 1. search DB by job.data.jobUuid
      // 2. update job completed_at = job.finishedOn
      // 3. update job status to "completed"
    });

    queueEvents.on('failed', async ({ jobId, failedReason }) => {
      const job = await queue.getJob(jobId);

      fastify.log.info(job.finishedOn); // dummy line to prevent sonarqube error
      // 1. search DB by job.data.jobUuid
      // 2. update job completed_at = job.finishedOn
      // 3. update job status to "failed"
    });
  }
};

export default fp(queueEventPlugin, { name: 'queue-event' });
