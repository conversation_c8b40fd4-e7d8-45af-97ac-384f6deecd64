import { baseConfig, completeRetention, failRetention, queueNames } from '#config/queue.config.js';
import IORedis from 'ioredis';
import { Queue } from 'bullmq';
import fp from 'fastify-plugin';

/**
 * Fastify plugin that initializes and configures BullMQ queues for job processing.
 * This plugin creates multiple queues based on predefined queue names and decorates
 * the Fastify instance with a queue object containing an add method for job submission.
 *
 * @param {Object} fastify - The Fastify instance to decorate with queue functionality
 * @param {Object} opts - Plugin options (currently unused)
 * @returns {Promise<void>} - A promise that resolves when the plugin is initialized
 */
const queuePlugin = async (fastify, opts) => {
  const connection = new IORedis(baseConfig);

  // Spliting the queue by different purpose for load balancing
  let queues = {};
  for (const queueName of queueNames) {
    const queue = new Queue(queueName, {
      connection: connection,
      removeOnComplete: completeRetention,
      removeOnFail: failRetention,
    });
    queues[queueName] = queue;
  }

  const add = async (queueName, jobName, params) => {
    if (!queueNames.includes(queueName)) {
      return false;
    }

    const queue = queues[queueName];

    await queue.add(jobName, params);

    return true;
  };

  fastify.decorate('queue', { add });
};

export default fp(queuePlugin, { name: 'queue' });
