import { CoreConstant } from '#src/modules/core/constants/index.js';

const { MODULE_METHODS } = CoreConstant;
const {
  CREATE,
  INDEX,
  OPTION,
  UPDATE,
  UPDATE_ACCESS_CONTROL,
  UPDATE_BASIC_INFORMATION,
  UPDATE_PERSONAL,
  UPDATE_PERMISSION,
  UPDATE_SAFETY,
  UPDATE_STATUS,
  UPDATE_THEMES,
  VIEW,
} = MODULE_METHODS;
/**
 * Represents a singleton class for generating success messages.
 */
class SuccessMessage {
  /**
   * Creates a new SuccessMessage instance or returns the existing one.
   * Implements the singleton pattern.
   */
  constructor() {
    this.templates = {
      [INDEX]: (module) => `Successfully retrieved ${module}`,
      [VIEW]: (module) => `Successfully retrieved a ${module}`,
      [CREATE]: (module) => `Successfully created ${module}`,
      [OPTION]: (module) => `Successfully retrieved ${module} options`,
      [UPDATE]: (module) => `Successfully updated ${module}`,
      [UPDATE_STATUS]: (module) => `Successfully updated ${module} status`,
      [UPDATE_BASIC_INFORMATION]: (module) => `Successfully updated ${module} basic information`,
      [UPDATE_PERSONAL]: (module) => `Successfully updated ${module} personal`,
      [UPDATE_SAFETY]: (module) => `Successfully updated ${module} safety`,
      [UPDATE_THEMES]: (module) => `Successfully updated ${module} themes`,
      [UPDATE_PERMISSION]: (module) => `Successfully updated ${module} permissions`,
      [UPDATE_ACCESS_CONTROL]: (module) => `Successfully updated ${module} access controls`,
    };
  }

  static getInstance() {
    if (!SuccessMessage._instance) {
      SuccessMessage._instance = new SuccessMessage();
    }
    return SuccessMessage._instance;
  }

  /**
   * Retrieves a success message template based on the module and operation type.
   * @param {string} module - The name of the module (e.g., 'user', 'product').
   * @param {string} type - The type of operation (e.g., 'view', 'create', 'update', 'updateStatus').
   * @param {...*} opts - Additional optional parameters that might be used in future template expansions.
   * @returns {string} The formatted success message.
   */
  getTemplate(module, type, ...opts) {
    const moduleName = module
      .split(/(?=[A-Z])/)
      .map((word, index) =>
        index === 0 ? word.charAt(0) + word.slice(1).toLowerCase() : word.toLowerCase(),
      )
      .join(' ');
    return this.templates[type]?.(moduleName, ...opts) || 'Operation successful';
  }
}
const instance = SuccessMessage.getInstance();
export default instance;
