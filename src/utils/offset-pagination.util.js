import { prepareSortOrder } from '#src/utils/query.util.js';

const DEFAULT_PAGE = 1;
const DEFAULT_LIMIT = 25;
const DEFAULT_SORT = [];

/**
 * Applies offset-based pagination to a database query.
 *
 * @param {Object} fastify - The Fastify instance.
 * @param {Object} model - The Sequelize model to query.
 * @param {Object} rawQuery - The raw query object containing pagination, sorting, and filtering parameters.
 * @param {Object} whereFilter - The where clause filter object for the query.
 * @param {Array} [includeFilter=[]] - An array of Sequelize include options for eager loading related models.
 * @returns {Promise<Object>} A promise that resolves to an object containing:
 *   - rows: The array of query results.
 *   - pagination: An object with pagination details (totalCount, totalPages, currentPage, limit).
 */
const applyOffsetPagination = async (fastify, model, rawQuery, whereFilter, includeFilter = []) => {
  const { page = DEFAULT_PAGE, limit = DEFAULT_LIMIT, sortBy = DEFAULT_SORT } = rawQuery;

  const offset = (+page - 1) * +limit;

  const order = prepareSortOrder(fastify, sortBy);

  const queryOptions = {
    order,
    where: whereFilter,
  };

  if (limit > 0) {
    queryOptions.offset = offset;
    queryOptions.limit = limit;
  }

  // Only add includeFilter when includes is defined
  if (includeFilter.length > 0) {
    queryOptions.include = includeFilter;
  }

  const { count, rows } = await model.findAndCountAll(queryOptions);

  const totalPages = limit > 0 ? Math.ceil(count / limit) : 1;
  const currentPage = limit > 0 ? +page : 1;

  return {
    rows,
    pagination: {
      totalCount: count,
      totalPages,
      currentPage: currentPage,
      limit: +limit,
    },
  };
};

export default applyOffsetPagination;
