import * as Template from '#db/postgres/templates/index.js';
import {
  LocalisationConstant,
  LocalisationRepository,
  SettingRepository,
} from '#src/modules/setting/index.js';
import { getCache, setCache } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CreditLimitTransactionService } from '#src/modules/credit-limit/index.js';
import { EntityConstant } from '#src/modules/entity/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

/**
 * Returns a list of database table templates to be used for merchant schema creation.
 *
 * @returns {Array<Object>} An array of table template objects, each containing table name,
 * column definitions, constraints, and indexes needed for scaffolding merchant database tables.
 *
 */
const getScaffoldTableList = () => {
  return [Template.CREDIT_LIMIT_TRANSACTIONS]; // Add scaffold tables here
};

/**
 * Creates database schema and tables required for the new merchant.
 *
 * @param {Object} fastify - The Fastify instance with database connections
 * @param {string} schema - The schema name to create using merchant code
 * @param {Array<Object>} [scaffoldTables=getScaffoldTableList()] - Optional array of table templates to scaffold.
 *                                                                 Defaults to the result of getScaffoldTableList()
 * @returns {Promise<void>} - Resolves when all tables, constraints, and indexes have been created
 *
 * @description
 * This function performs the following operations:
 * 1. Creates a new PostgreSQL schema with the provided name
 * 2. Creates required tables from predefined templates
 * 3. Adds constraints and indexes to the created tables
 *
 * Each table template in the scaffoldTables array should contain:
 * - table: String name of the table
 * - columns: Object defining table columns and their properties
 * - constraints: Array of constraint definitions
 * - indexes: Array of index definitions
 */
const scaffoldTable = async (fastify, schema, scaffoldTables = getScaffoldTableList()) => {
  const sequelize = fastify.psql.connection;

  // Create new schema
  await sequelize.createSchema(schema, { ifNotExist: true });

  await withTransaction(fastify, {}, async (transaction) => {
    for (const scaffoldTable of scaffoldTables) {
      const queryInterface = sequelize.getQueryInterface();

      // Create table
      await queryInterface.createTable(scaffoldTable.table, scaffoldTable.columns, {
        schema: schema,
        transaction: transaction,
      });

      // Create constraints
      for (const constraintObj of scaffoldTable.constraints) {
        await queryInterface.addConstraint(`${schema}.${scaffoldTable.table}`, {
          ...constraintObj,
          transaction: transaction,
        });
      }

      // Create indexes
      for (const indexObj of scaffoldTable.indexes) {
        await queryInterface.addIndex(`${schema}.${scaffoldTable.table}`, indexObj.fields, {
          ...indexObj.options,
          transaction: transaction,
        });
      }
    }
  });
};

/**
 * Assigns an initial credit limit to a newly created merchant entity.
 *
 * @param {Object} fastify - The Fastify instance with database connections
 * @param {Object} entity - The merchant entity object
 * @param {number} creditLimit - The amount of credit to assign to the merchant
 * @param {string} createdBy - ID of the user who is assigning the credit limit
 * @returns {Promise<void>} - Resolves when the credit limit has been assigned
 *
 */
const assignCreditLimit = async (fastify, entity, creditLimit, createdBy) => {
  if (creditLimit > 0) {
    // add credit limit to merchant
    await CreditLimitTransactionService.createTransaction(
      fastify,
      entity,
      creditLimit,
      1, // hardcoded as will be always using USD during create
      createdBy,
    );
  }
};

/**
 * Determines whether a specific merchant setting should be copied from the source merchant.
 *
 * @param {string} field - The setting field name to check
 * @param {Object} copyConfig - Configuration for the copy operation
 * @param {boolean} copyConfig.copy - Master flag indicating if any copying should occur
 * @param {string} copyConfig.option - Copy option ('all' or 'custom')
 * @param {Object} copyConfig.setting - Settings configuration when option is 'custom'
 * @returns {boolean} - True if the setting should be copied, false otherwise
 *
 */
const shouldCopyMerchantSetting = (field, copyConfig) => {
  // if no need copy all, return false
  if (copyConfig.copy !== true) {
    return false;
  }

  // if copy all option, return true
  if (copyConfig.option === 'all') {
    return true;
  }

  // check custom copy option
  switch (field) {
    case 'language':
    case 'timezone':
    case 'colorScheme':
    case 'twoFactorEnforcement':
    case 'usernameMasking':
    case 'emailMasking':
    case 'accessControl':
      return copyConfig.setting[field];

    case 'lowCreditThreshold1':
    case 'lowCreditThreshold2':
      return copyConfig.setting.creditLimitAlert;

    case 'passwordMinimumLength':
    case 'passwordRequiredUppercase':
    case 'passwordRequiredLowercase':
    case 'passwordRequiredNumber':
    case 'passwordRequiredSpecial':
      return copyConfig.setting.passwordPolicy;

    case 'mobileMasking':
    case 'mobileMaskingStart':
    case 'mobileMaskingEnd':
      return copyConfig.setting.mobileMasking;

    default:
      return false;
  }
};

/**
 * Adds setting to the collection of settings to be created for the new merchant.
 *
 * @param {Array<Object>} customSettingData - Array to collect settings for bulk creation
 * @param {Object} setting - The setting definition from the source merchant
 * @param {string} setting.field - The field name of the setting
 * @param {string} setting.id - The unique identifier of the setting
 * @param {Object} customSetting - The custom setting value from the source merchant
 * @param {string|number|boolean} customSetting.value - The value of the setting
 * @param {string|null} customSetting.localisationId - The localisation ID for currency-specific settings
 * @param {string} entityId - The ID of the current merchant entity
 * @param {boolean} copy - Whether to copy the value from the source merchant or use default
 *
 */
const addCopySetting = (customSettingData, setting, customSetting, entityId, copy) => {
  customSettingData.push({
    parentId: setting.id,
    value: copy ? customSetting.value : EntityConstant.MERCHANT_SETTING_DEFAULT[setting.field],
    localisationId: customSetting.localisationId,
    entityId: entityId,
  });
};

/**
 * Create default merchant settings or copies merchant settings.
 *
 * @param {Object} fastify - The Fastify instance with database connections
 * @param {Object} params - Parameters for the merchant settings copy operation
 * @param {Object} params.entity - The current entity object
 * @param {string} params.entity.id - The unique identifier for the current merchant
 * @param {Object} params.copyConfig - Configuration for copying settings
 * @param {boolean} params.copyConfig.copy - Whether to copy settings from another merchant
 * @param {string} params.copyConfig.entityId - The source merchant ID to copy settings from
 * @param {string} params.copyConfig.option - Copy option ('all' or 'custom')
 * @param {Object} params.copyConfig.setting - Specific settings to copy when option is 'custom'
 * @param {string} params.createdBy - ID of the user performing the copy operation
 * @param {Array<Object>} supportedCurrencies - List of currencies supported by the current merchant
 * @param {string} supportedCurrencies[].parentId - The ID of each supported currency
 * @returns {Promise<void>} - Resolves when all settings have been copied and saved
 *
 */
const copyMerchantSetting = async (fastify, params, supportedCurrencies) => {
  const entityId = params.entity.id;
  const copyConfig = params.copyConfig;
  const supportedCurrencyIds = supportedCurrencies.map((currency) => currency.parentId);
  const copySettings = Object.keys(EntityConstant.MERCHANT_SETTING_DEFAULT);

  const customSettingData = [];
  const sourceSettings = await SettingRepository.findByCategoryAndAccess(fastify, {
    'filter_customSettings.entityId_eq': copyConfig.entityId,
  });

  // Copy settings
  for (const field of copySettings) {
    const setting = sourceSettings.filter((data) => data.field === field)[0];
    const copy = shouldCopyMerchantSetting(field, copyConfig);

    if (
      field === 'mobileMasking' ||
      field === 'mobileMaskingStart' ||
      field === 'mobileMaskingEnd'
    ) {
      // mobile settings with currency
      for (const customSetting of setting.customSettings) {
        if (supportedCurrencyIds.includes(customSetting.localisationId)) {
          addCopySetting(customSettingData, setting, customSetting, entityId, copy);
        }
      }
    } else {
      // other settings without currency
      addCopySetting(customSettingData, setting, setting.customSettings[0], entityId, copy);
    }
  }

  // insert merchant settings
  await withTransaction(fastify, {}, async (transaction) => {
    return await SettingRepository.bulkCreate(fastify, customSettingData, {
      transaction,
      authInfoId: params.createdBy,
    });
  });
};

/**
 * Exports internal functions for unit testing purposes.
 */
export const exportForUnitTest = {
  addCopySetting,
  assignCreditLimit,
  copyMerchantSetting,
  getScaffoldTableList,
  scaffoldTable,
  shouldCopyMerchantSetting,
};

/**
 * Creates new merchant settings.
 *
 * @param {Object} fastify - The Fastify instance with database connections
 * @param {Object} params - Parameters for merchant creation
 * @param {Object} params.entity - The entity object with merchant details
 * @param {string} params.entity.id - The unique identifier for the merchant
 * @param {string} params.entity.code - The merchant code used for schema creation
 * @param {number} params.creditLimit - The initial credit limit to assign to the merchant
 * @param {Object} params.copyConfig - Configuration for copying settings from another merchant
 * @param {boolean} params.copyConfig.copy - Whether to copy settings from another merchant
 * @param {string} params.copyConfig.entityId - The source merchant ID to copy settings from
 * @param {string} params.copyConfig.option - Copy option ('all' or 'custom')
 * @param {Object} params.copyConfig.setting - Specific settings to copy when option is 'custom'
 * @param {string} params.createdBy - ID of the user creating the merchant
 * @param {Object} [functions] - Optional function overrides for testing
 * @param {Function} [functions.scaffoldTableFn] - Function to create database schema and tables
 * @param {Function} [functions.assignCreditLimitFn] - Function to assign initial credit limit
 * @param {Function} [functions.copyMerchantSettingFn] - Function to copy merchant settings
 * @returns {Promise<string>} - A message indicating the completion status
 * @throws {Error} If any step of the merchant creation process fails
 *
 * @description
 * This function performs several steps to set up a new merchant:
 * 1. Creates database schema and tables for the merchant
 * 2. Assigns initial credit limit if specified
 * 3. Copies settings from another merchant based on copyConfig
 * 4. Sets up other required configurations
 *
 */
export const createMerchantJob = async (
  fastify,
  params,
  {
    // Defined like this so that unit test can mock these functions
    scaffoldTableFn = scaffoldTable,
    assignCreditLimitFn = assignCreditLimit,
    copyMerchantSettingFn = copyMerchantSetting,
  } = {},
) => {
  const entity = params.entity;

  // Pending QPLY-1112: Step: query for bulkJob
  // const bulkJob = BulkJobRepository.findOne(params.bulkJobId);

  try {
    // step 1: scaffold table
    // Pending QPLY-1112: Step:
    // if (bulkJob.progress < EntityConstant.MERCHANT_JOB_PROGRESS.SCAFFOLD_TABLE) {
    await scaffoldTableFn(fastify, entity.code);
    // then BulkJobRepository.update(bulkJob, { progress: EntityConstant.MERCHANT_JOB_PROGRESS.SCAFFOLD_TABLE });
    // }

    // step 2: assign credit limit
    // Pending QPLY-1112: Step:
    // if (bulkJob.progress < EntityConstant.MERCHANT_JOB_PROGRESS.ADJUST_CREDIT_LIMIT) {
    await assignCreditLimitFn(fastify, entity, params.creditLimit, params.createdBy);
    // then BulkJobRepository.update(bulkJob, { progress: EntityConstant.MERCHANT_JOB_PROGRESS.ADJUST_CREDIT_LIMIT });
    // }

    // step 3: initiate and copy setting
    const supportedCurrencies = await LocalisationRepository.findAll(fastify, {
      'filter_localisation.category_eq': LocalisationConstant.LOCALISATION_CATEGORIES.CURRENCY,
      filter_entityId_eq: entity.id,
      limit: 0, // all records
    });

    // step 3a: copy merchant settings
    // Pending QPLY-1112: Step:
    // if (bulkJob.progress < EntityConstant.MERCHANT_JOB_PROGRESS.COPY_MERCHANT_SETTING) {
    await copyMerchantSettingFn(fastify, params, supportedCurrencies.rows);
    // then BulkJobRepository.update(bulkJob, { progress: EntityConstant.MERCHANT_JOB_PROGRESS.COPY_MERCHANT_SETTING });
    // }

    // step 3b: copy other settings (access control, promotion, vip setting etc)
    // add in copy code here

    // update entities status active

    // generate setup event

    return 'Completed with copying';
  } catch (error) {
    // update merchant status to failed

    // generate failed event
    fastify.log.error(error, `Create merchant job failed. Entity code: ${entity.code}`);
    throw error;
  }
};

/**
 * Checks if the entity's credit limit has fallen below configured thresholds and triggers alerts
 *
 * @param {Object} fastify - Fastify instance with Redis connection
 * @param {Object} params - Parameters for the threshold check
 * @param {string} params.entityId - The ID of the entity to check
 * @param {string|number} params.creditLimit - The current credit limit value to check against thresholds
 * @returns {Promise<void>} - Resolves when the check is complete
 *
 * @description
 * This function compares the entity's current credit limit against two configurable thresholds:
 * - low_credit_threshold_1: First warning level (higher value)
 * - low_credit_threshold_2: Critical warning level (lower value)
 *
 * If the credit limit falls below either threshold, an alert is triggered.
 * To prevent repeated alerts, a cache entry is created with a daily expiration.
 */
export const checkCreditLimitThreshold = async (fastify, params) => {
  const { entityId, creditLimit } = params;

  // add get merchant setting code then set merchant code
  // const merchant = await EntityRepository.___();
  const merchantCode = 'test';

  const customSettings = await SettingRepository.findByCategoryAndAccess(fastify, {
    'filter_customSettings.entityId_eq': entityId,
    filter_category_eq: 'general',
    filter_accessLevels_overlap: 'merchant',
  });

  const setting = customSettings
    .filter((setting) => {
      return setting.field === 'lowCreditThreshold1' || setting.field === 'lowCreditThreshold2';
    })
    .map((setting) => {
      return { [setting.field]: setting.customSettings.value };
    })
    .reduce((accumulator, setting) => {
      return { ...accumulator, ...setting };
    });

  let cacheKey = null;

  if (creditLimit < setting.lowCreditThreshold2) {
    cacheKey = `${merchantCode}:credit-threshold2`;
  } else if (creditLimit < setting.lowCreditThreshold1) {
    cacheKey = `${merchantCode}:credit-threshold1`;
  }

  const cacheHit = await getCache(fastify.redis, cacheKey);
  if (cacheKey && !cacheHit) {
    // cache not found, alert relavent stakeholder
    // add alert code here

    // cache for 24 hours to prevent repeated alerts
    await setCache(fastify.redis, cacheKey, true, CoreConstant.CACHE_SECOND.DAILY);
  }
};
