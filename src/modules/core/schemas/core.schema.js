/**
 * Common entity properties used across many schemas.
 * @constant
 * @type {object}
 */
export const COMMON_PROPERTIES = {
  id: { type: 'string', format: 'uuid' },
  createdAt: { type: 'string', format: 'date-time' },
  createdBy: { type: 'string', format: 'uuid' },
  updatedAt: { type: 'string', format: 'date-time' },
  updatedBy: { type: 'string', format: 'uuid' },
};

/**
 * Standard error response reference for OpenAPI/Swagger.
 * @constant
 * @type {object}
 */
export const ERROR_RESPONSE = {
  '4xx': { $ref: 'ErrorResponse' },
  '5xx': { $ref: 'ErrorResponse' },
};

/**
 * Standard pagination metadata returned in paginated responses.
 * @constant
 * @type {object}
 */
export const OFFSET_PAGINATION_META_PROPERTIES = {
  totalCount: { type: 'integer' },
  totalPages: { type: 'integer' },
  currentPage: { type: 'integer' },
  limit: { type: 'integer' },
};

/**
 * Standard pagination query parameters for offset-based pagination.
 * @constant
 * @type {object}
 */
export const OFFSET_PAGINATION_QUERY_PARAMS = {
  page: { type: 'string', description: 'Current page number', default: 1 },
  limit: { type: 'string', description: 'Items per page', default: 25 },
  sortBy: {
    oneOf: [{ type: 'string' }, { type: 'array' }],
    description: 'Sorting field in the format (field:order) (e.g., name:asc)',
  },
};

/**
 * Request parameter validation for UUID route params.
 * @constant
 * @type {object}
 */
export const REQ_PARAM_UUID = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
  },
  required: ['id'],
};

/**
 * Standard update (PATCH/PUT) API response schema.
 * @constant
 * @type {object}
 */
export const UPDATE_RESPONSE = {
  200: {
    description: 'Success response',
    type: 'object',
    properties: {
      message: { type: 'string' },
    },
  },
  ...ERROR_RESPONSE,
};

/**
 * Standard view (GET) API response schema.
 * @function
 * @param {object} [customProperties={}] - Custom data properties to include.
 * @returns {object} View response schema object.
 */
export const VIEW_RESPONSE = (customProperties = {}) => ({
  200: {
    description: 'Success response',
    type: 'object',
    properties: {
      message: { type: 'string' },
      data: customProperties,
    },
  },
  ...ERROR_RESPONSE,
});

/**
 * Creates a pagination metadata schema with optional additional properties.
 * @function
 * @param {object} [additionalProperties={}] - Additional properties to include.
 * @returns {object} Combined pagination metadata schema.
 */
export const createOffsetPaginationResponseSchema = (additionalProperties = {}) => ({
  type: 'object',
  properties: {
    pagination: {
      type: 'object',
      properties: {
        ...OFFSET_PAGINATION_META_PROPERTIES,
      },
    },
    ...additionalProperties,
  },
});
