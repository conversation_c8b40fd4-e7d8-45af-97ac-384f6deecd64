import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const CORE_ERROR_DEF = {
  notFound: ['10002', 'Data not found. (ID: %s)', 404],
  versionConflict: [
    '10013',
    'The record has been modified since last retrieval. Please refresh and try again. (Current Version: %s)',
    409,
  ],
};

export const coreError = createModuleErrors(MODULE_NAMES.CORE, CORE_ERROR_DEF);
