export const ACCESS_LEVELS = {
  MERCHANT: 'merchant',
  ORGANIZATION: 'organization',
  ROOT: 'root',
  USER: 'user',
};

export const ACCESS_LEVEL_KEYS = {
  user: 'user',
  member: 'member',
  webhook: 'webhook',
};

export const CACHE_SECOND = {
  SHORT: 10, // For rapidly changing data or debounce-type caching
  MEDIUM: 30, // 30 second – suitable for moderately volatile data
  STANDARD: 60, // 1 minute – good default for most general cache
  LONG: 3600, // 1 hour – stable data that changes infrequently
  DAILY: 86400, // 24 hours – rarely changing reference data
  WEEKLY: 604800, // 7 days – archive-type or external lookup cache
  NEVER: 0, // Used when caching is disabled
};

export const COMMON_STATUSES = {
  ACTIVE: 'active',
  DELETED: 'deleted',
  INACTIVE: 'inactive',
};

export const COMMON_TRANSACTION_STATUSES = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  VOIDED: 'voided',
};

export const MODULE_METHODS = {
  CREATE: 'create',
  DELETE: 'delete',
  INDEX: 'index',
  OPTION: 'option',
  UPDATE: 'update',
  UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
  UPDATE_PERSONAL: 'updatePersonal',
  UPDATE_SAFETY: 'updateSafety',
  UPDATE_STATUS: 'updateStatus',
  UPDATE_THEMES: 'updateThemes',
  VIEW: 'view',
};

export const MODULE_NAMES = {
  ACCESS_CONTROL: 'accessControls',
  CORE: 'core',
  CREDIT_LIMIT: 'creditLimits',
  CREDIT_LIMIT_TRANSACTION: 'creditLimitTransactions',
  DEVELOPER_HUB: 'developerHubs',
  LOCALISATION: 'localisations',
  SETTING: 'settings',
};

export const REMARK_STATUSES = {
  ACTIVE: 'active',
  ARCHIVED: 'archived',
};

export const REMARK_TYPE = {
  AUDIT: 'audit',
  NOTE: 'note',
  SECURITY: 'security',
  SYSTEM: 'system',
  WARNING: 'warning',
};

export const REMARKABLE_TYPE = {
  IP_ACCESS_CONTROL: 'ip_access_control',
};
