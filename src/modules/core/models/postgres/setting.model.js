import { DataTypes, Model, Sequelize } from 'sequelize';

import { ACCESS_LEVELS } from '#src/modules/core/constants/core.constant.js';
import { SettingConstant } from '#src/modules/setting/constants/index.js';

export default function (fastify, instance) {
  class Setting extends Model {
    static associate(models) {
      Setting.hasMany(models.CustomSetting, {
        foreignKey: 'parentId',
        as: 'customSettings',
      });
    }

    toSafeObject() {
      const values = { ...this.get() };
      // Remove meta/audit fields from Setting
      delete values.createdBy;
      delete values.updatedBy;
      delete values.createdAt;
      delete values.updatedAt;
      delete values.deletedAt;
      // Clean customSettings if present
      if (values.customSettings && values.customSettings.length > 0) {
        const customSettings = [];
        for (const customSetting of values.customSettings) {
          const { value, version, createdBy, updatedBy, createdAt, updatedAt, localisationId } =
            customSetting;
          customSettings.push({
            value,
            version,
            createdBy,
            updatedBy,
            createdAt,
            updatedAt,
            localisationId,
          });
        }
        values.customSettings = customSettings;
      }
      return values;
    }
  }

  Setting.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      category: {
        type: DataTypes.ENUM(Object.values(SettingConstant.SETTING_CATEGORIES)),
        allowNull: false,
      },
      field: {
        type: DataTypes.STRING(50),
        allowNull: false,
        validate: {
          len: [1, 50],
          notEmpty: true,
        },
      },
      accessLevels: {
        type: DataTypes.ARRAY(DataTypes.STRING),
        allowNull: false,
        defaultValue: ['root'],
        validate: {
          isValidAccessLevel(value) {
            if (
              !Array.isArray(value) ||
              !value.every((level) => Object.values(ACCESS_LEVELS).includes(level))
            ) {
              throw new Error('Invalid access level');
            }
          },
        },
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'Setting',
      tableName: 'settings',
      underscored: true,
      timestamps: true,
      paranoid: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['category', 'field'],
        },
      ],
      hooks: {},
    },
  );

  return Setting;
}
