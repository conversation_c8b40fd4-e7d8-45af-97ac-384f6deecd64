import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const CREDIT_LIMIT_ERROR_DEF = {
  insufficientBalance: [
    '70008',
    'Insufficient credit balance to complete the transaction with ID: %s',
    400,
  ],
  invalidExRate: ['70010', 'Invalid exchange rate: %s', 400],
  updateFail: ['70009', 'Failed to update credit limit with ID: %s', 400],
};

export const creditLimitError = createModuleErrors(
  MODULE_NAMES.CREDIT_LIMIT,
  CREDIT_LIMIT_ERROR_DEF,
);
