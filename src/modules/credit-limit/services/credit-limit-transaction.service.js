import { BigNumber } from 'bignumber.js';

import {
  CreditLimitConstant,
  CreditLimitRepository,
  CreditLimitTransactionRepository,
} from '#src/modules/credit-limit/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CreditLimitError } from '#src/modules/credit-limit/errors/index.js';

/**
 * Adjusts an entity's credit limit balance with optimistic locking to handle concurrent updates
 *
 * @async
 * @param {Object} fastify - The Fastify instance with database connections
 * @param {string} entityId - The UUID of the entity whose credit limit to adjust
 * @param {string|number} amount - The amount to adjust (positive for increase, negative for decrease)
 * @param {boolean} allowNegative - Whether to allow the balance to go negative
 * @param {string} creditTransactionId - The ID of the credit transaction for error reference
 * @returns {Promise<Object>} Object containing the before and after balance values
 * @throws {CreditLimitError} When there's insufficient balance or update fails after retries
 *
 * @description
 * This function implements optimistic locking to safely update credit balances:
 * 1. Retrieves the current credit limit record
 * 2. Checks if there's sufficient balance for deductions
 * 3. Attempts to update with version checking
 * 4. Retries on version conflicts up to OPTIMISTIC_RETRY times
 * 5. Returns the before and after balance values on success
 */
const adjustCredit = async (fastify, entityId, amount, allowNegative, creditTransactionId) => {
  let beforeBalance = 0;
  let newBalance = 0;

  const adjustAmount = new BigNumber(amount);

  // Optimistic locking
  for (let attempt = 0; attempt < CreditLimitConstant.OPTIMISTIC_RETRY; attempt++) {
    const entityCreditLimit = await CreditLimitRepository.findByEntityId(fastify, entityId);

    const creditBalance = new BigNumber(entityCreditLimit.credit);

    // Check enough balance when deduct
    if (
      !allowNegative &&
      adjustAmount.isLessThan(0) &&
      creditBalance.plus(adjustAmount).isLessThan(0)
    ) {
      // Insufficient balance
      throw CreditLimitError.insufficientBalance(creditTransactionId);
    }

    beforeBalance = creditBalance.toString();
    newBalance = creditBalance.plus(adjustAmount).toString();

    try {
      await CreditLimitRepository.updateByEntityId(entityCreditLimit, {
        credit: newBalance,
        version: entityCreditLimit.version,
      });

      // Update success
      return {
        beforeBalance: beforeBalance,
        newBalance: newBalance,
      };
    } catch (error) {
      if (error.code !== '10013') {
        // If not version conflict error, direct stop
        throw error;
      }
    }
  }

  // Optimistic locking failed
  throw CreditLimitError.updateFail(creditTransactionId);
};

/**
 * Adds a job to the queue to check if the entity's credit limit has reached configured thresholds
 *
 * @async
 * @param {Object} fastify - The Fastify instance with queue functionality
 * @param {string} entityId - The UUID of the entity to check thresholds for
 * @param {string|number} creditLimit - The current credit limit balance to check against thresholds
 * @returns {Promise<void>} A promise that resolves when the job has been added to the queue
 *
 */
const addCheckCreditLimitThresholdJob = async (fastify, entityId, creditLimit) => {
  // Create job to check credit limit threshold
  await fastify.queue.add('entity-queue', 'check-balance', {
    entityId: entityId,
    creditLimit: creditLimit,
  });
};

/**
 * Exports internal utility functions for unit testing purposes.
 */
export const exportForUnitTest = {
  adjustCredit,
  addCheckCreditLimitThresholdJob,
};

/**
 * Creates a credit limit transaction and updates the entity's credit limit balance.
 *
 * @async
 * @param {Object} fastify - The Fastify instance with database connections and queue
 * @param {Object} entity - The merchant entity object
 * @param {number|string} baseAmount - The base amount of the transaction
 * @param {number|string} exchangeRate - The exchange rate to apply to the transaction
 * @param {string} createdBy - ID of the user who is creating the transaction
 * @param {string|null} [referenceId=null] - Optional reference ID for the transaction
 * @param {boolean} [allowNegative=false] - Whether to allow the balance to go negative
 * @returns {Promise<Object>} The updated credit limit transaction record
 * @throws {CreditLimitError.invalidExRate} When the exchange rate is less than or equal to zero
 * @throws {CreditLimitError.insufficientBalance} When there's insufficient balance for deduction
 * @throws {CreditLimitError.updateFail} When the update fails after maximum retry attempts
 *
 * @description
 * This function performs the following operations:
 * 1. Calculates the actual amount based on the base amount and exchange rate
 * 2. Creates a pending transaction record
 * 3. Attempts to update the entity's credit balance with optimistic locking
 * 4. Updates the transaction status to COMPLETED or FAILED based on the result
 * 5. Adds a job to check if the new balance is below configured thresholds
 *
 * The function uses optimistic locking with multiple retry attempts to handle concurrent updates.
 * If the balance would go negative and allowNegative is false, the transaction fails.
 */
export const createTransaction = async (
  fastify,
  entity,
  baseAmount,
  exchangeRate,
  createdBy,
  referenceId = null,
  allowNegative = false,
) => {
  const exRate = new BigNumber(exchangeRate);
  if (exRate.isLessThanOrEqualTo(0)) {
    // Invalid exchange rate
    throw CreditLimitError.invalidExRate(exchangeRate);
  }

  // Calculate exchange rate amount
  const amount = new BigNumber(baseAmount).dividedBy(exchangeRate).decimalPlaces(8).toString();

  let transactionType = CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES.DEBIT;
  if (Number(amount) > 0) {
    transactionType = CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES.CREDIT;
  }

  // Create pending transaction
  const creditLimitTransaction = await CreditLimitTransactionRepository.create(
    fastify,
    {
      entityId: entity.id,
      referenceId: referenceId,
      type: transactionType,
      baseAmount: baseAmount,
      exchangeRate: exchangeRate,
      amount: amount,
      status: CoreConstant.COMMON_TRANSACTION_STATUSES.PENDING,
    },
    entity.code,
    { authInfoId: createdBy },
  );

  let updateResult = {};
  try {
    updateResult = await adjustCredit(
      fastify,
      entity.id,
      amount,
      allowNegative,
      creditLimitTransaction.id,
    );
  } catch (error) {
    // Update transaction status to failed
    await CreditLimitTransactionRepository.update(
      creditLimitTransaction,
      {
        status: CoreConstant.COMMON_TRANSACTION_STATUSES.FAILED,
      },
      { authInfoId: createdBy },
    );

    throw error;
  }

  // Update transaction status to completed
  const res = await CreditLimitTransactionRepository.update(
    creditLimitTransaction,
    {
      beforeBalance: updateResult.beforeBalance,
      afterBalance: updateResult.newBalance,
      status: CoreConstant.COMMON_TRANSACTION_STATUSES.COMPLETED,
    },
    { authInfoId: createdBy },
  );

  // Check for credit limit threshold
  await addCheckCreditLimitThresholdJob(fastify, entity.id, updateResult.newBalance);

  return res;
};
