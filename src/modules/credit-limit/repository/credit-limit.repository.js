/**
 * Finds a credit limit record by entity ID
 *
 * @param {Object} fastify - Fastify instance with database connections
 * @param {string} entityId - The unique identifier of the entity to find
 * @returns {Promise<Object|null>} The credit limit record if found, null otherwise
 */
export const findByEntityId = async (fastify, entityId) => {
  return await fastify.psql.CreditLimit.findOne({
    where: {
      entity_id: entityId,
    },
  });
};

/**
 * Updates a credit limit record with new data
 *
 * @param {Object} modelData - The model instance to update
 * @param {Object} updateData - The data to update the model with
 * @param {Object} [options={}] - Optional Sequelize options for the update operation
 * @returns {Promise<Object>} The updated record if successful
 */
export const updateByEntityId = async (modelData, updateData, options = {}) => {
  return await modelData.update(updateData, {
    ...options,
  });
};
