/**
 * Creates a new credit limit transaction record
 *
 * @param {Object} fastify - Fastify instance with database connections
 * @param {Object} data - The transaction data to create
 * @param {string} schema - Database schema name to use for this operation
 * @param {Object} [options={}] - Additional options for the create operation
 * @returns {Promise<Object>} The created transaction record
 *
 */
export const create = async (fastify, data, schema, options = {}) => {
  return await fastify.psql.CreditLimitTransaction.schema(schema).create(data, {
    ...options,
  });
};

/**
 * Updates an existing credit limit transaction record
 *
 * @param {Object} modelData - The transaction model instance to update
 * @param {Object} updateData - The data to update the transaction with
 * @param {Object} [options={}] - Additional options for the update operation
 * @returns {Promise<Object>} The updated transaction record
 */
export const update = async (modelData, updateData, options = {}) => {
  return await modelData.update(updateData, {
    ...options,
  });
};
