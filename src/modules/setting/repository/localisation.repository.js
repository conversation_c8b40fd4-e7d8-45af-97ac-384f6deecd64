import applyOffsetPagination from '#src/utils/offset-pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Retrieves all localisation entries with pagination based on the given query parameters.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query parameters for filtering and pagination.
 * @returns {Promise<Object>} A promise that resolves to the paginated result of localisation entries.
 */
export const findAll = async (fastify, query) => {
  const { CustomLocalisation } = fastify.psql;

  const includes = [
    {
      association: 'localisation',
      required: true,
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    CustomLocalisation,
    includes,
  );

  return await applyOffsetPagination(
    fastify,
    CustomLocalisation,
    query,
    whereFilter,
    includeFilter,
  );
};

/**
 * Retrieves a localisation entry based on the given query parameters.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query parameters for filtering the localisation entry.
 * @returns {Promise<Object|null>} A promise that resolves to the found localisation entry or null if not found.
 */
export const findById = async (fastify, query) => {
  const { CustomLocalisation } = fastify.psql;

  const includes = [
    {
      model: fastify.psql.Localisation,
      as: 'localisation',
      required: true,
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    CustomLocalisation,
    includes,
  );

  const record = await CustomLocalisation.findOne({
    where: whereFilter,
    include: includeFilter,
  });

  return record;
};

/**
 * Retrieves the base currency localisation entry from the database.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @returns {Promise<Object|null>} A promise that resolves to the base currency localisation entry or null if not found.
 */
export const findBaseCurrency = async (fastify) => {
  return await fastify.psql.Localisation.findOne({
    where: {
      code: fastify.config.BASE_CURRENCY,
    },
  });
};

/**
 * Retrieves an active localisation entry based on the given query parameters.
 *
 * @param {Object} fastify - The Fastify instance providing access to the database.
 * @param {Object} query - The query parameters for filtering the active localisation entry.
 * @returns {Promise<Object|null>} A promise that resolves to the found active localisation entry or null if not found.
 */
export const findActiveByParentId = async (fastify, query) => {
  const { CustomLocalisation } = fastify.psql;

  const { where: whereFilter } = buildWhereFromFilters(
    { ...query, filter_status_eq: 'active' },
    CustomLocalisation,
  );

  return await CustomLocalisation.findOne({
    where: whereFilter,
  });
};

/**
 * Updates a model instance with new data and records the user who made the change.
 *
 * @param {Object} modelData - The model instance to be updated.
 * @param {Object} updateData - An object containing the new data to update the model with.
 * @param {Object} authInfo - The user object representing the user making the update.
 * @param {string|number} authInfo.id - The unique identifier of the user.
 * @returns {Promise<Object>} A promise that resolves to the updated model instance.
 */
export const update = async (modelData, updateData, authInfo) => {
  const options = {
    authInfoId: authInfo.id,
  };
  return await modelData.update(updateData, options);
};
