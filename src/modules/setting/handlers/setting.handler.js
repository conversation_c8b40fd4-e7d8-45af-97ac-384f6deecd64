import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { SettingConstant } from '#src/modules/setting/constants/index.js';
import { SettingService } from '#src/modules/setting/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { SETTING },
  MODULE_METHODS: { INDEX, OPTION, UPDATE_PERSONAL, UPDATE_SAFETY, UPDATE_THEMES },
} = CoreConstant;
const {
  SETTING_CATEGORIES: { PERSONAL, SAFETY, THEMES },
} = SettingConstant;

const MODULE = SETTING;

/**
 * Handles the retrieval of setting data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the setting data and pagination details.
 */
export const index = async (request, reply) => {
  const {
    params: { category },
  } = request;

  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request, category === PERSONAL);

  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => SettingService.index(request, category),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Creates a function to update settings for a specific category.
 *
 * @param {string} category - The category of settings to update (e.g., PERSONAL, SAFETY, THEMES).
 * @param {string} method - The method name for the update operation.
 * @returns {Function} An async function that handles the update request for the specified category.
 *   The returned function takes two parameters:
 *   @param {Object} request - The incoming request object.
 *   @param {Object} reply - The reply object used to send the response.
 *   @returns {Promise<Object>} A promise that resolves to the response from handleServiceResponse.
 */
const updateSettingByCategory = (category, method) => async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => SettingService.update(request, category),
    module: MODULE,
    method,
  });

/**
 * Updates personal settings for the user.
 * This function is created by partially applying the updateSettingByCategory function
 * with the PERSONAL category and UPDATE_PERSONAL method.
 *
 * @function
 * @async
 * @param {Object} request - The incoming HTTP request object.
 * @param {Object} reply - The reply object used to send the HTTP response.
 * @returns {Promise<Object>} A promise that resolves to the response object
 *                            containing the result of the personal settings update.
 */
export const updatePersonal = updateSettingByCategory(PERSONAL, UPDATE_PERSONAL);

/**
 * Updates safety settings for the user.
 * This function is created by partially applying the updateSettingByCategory function
 * with the SAFETY category and UPDATE_SAFETY method.
 *
 * @function
 * @async
 * @param {Object} request - The incoming HTTP request object.
 * @param {Object} reply - The reply object used to send the HTTP response.
 * @returns {Promise<Object>} A promise that resolves to the response object
 *                            containing the result of the personal settings update.
 */
export const updateSafety = updateSettingByCategory(SAFETY, UPDATE_SAFETY);

/**
 * Updates themes settings for the user.
 * This function is created by partially applying the updateSettingByCategory function
 * with the THEMES category and UPDATE_THEMES method.
 *
 * @function
 * @async
 * @param {Object} request - The incoming HTTP request object.
 * @param {Object} reply - The reply object used to send the HTTP response.
 * @returns {Promise<Object>} A promise that resolves to the response object
 *                            containing the result of the personal settings update.
 */
export const updateThemes = updateSettingByCategory(THEMES, UPDATE_THEMES);

/**
 * Handles the retrieval of setting information.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves to the response object containing the setting information.
 */
export const options = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => SettingService.list(request),
    module: MODULE,
    method: OPTION,
  });
