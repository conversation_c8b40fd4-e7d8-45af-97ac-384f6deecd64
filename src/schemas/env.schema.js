// Define the environment schema to validate environment variables
const envSchema = {
  type: 'object',
  properties: {
    API_KEY_NAME: { type: 'string', default: 'x-api-key' },
    APP_NAME: { type: 'string', default: 'Fastify API' },
    AWS_ACCESS_KEY_ID: { type: 'string' },
    AWS_BUCKET: { type: 'string' },
    AWS_REGION: { type: 'string' },
    AWS_S3_PATH: { type: 'string' },
    AWS_SECRET_ACCESS_KEY: { type: 'string' },
    BASE_CURRENCY: { type: 'string' },
    BASE_URL: { type: 'string' },
    DB_DIALECT: { type: 'string', default: 'postgres' },
    DEFAULT_LOCALE: { type: 'string', default: 'en-MY' },
    DOCKER_CONTAINER: { type: 'boolean', default: false },
    DOCKER_PORT: { type: 'number', default: 3000 },
    ELASTIC_STACK_VERSION: { type: 'string', default: '8.16.1' },
    EXTERNAL_DOCS_DESCRIPTION: { type: 'string' },
    EXTERNAL_DOCS_URL: { type: 'string' },
    FASTIFY_ADDRESS: { type: 'string', default: '0.0.0.0' },
    FASTIFY_PORT: { type: 'number', default: 3000 },
    IMAGE_TAG: { type: 'string', enum: ['dev', 'latest'], default: 'dev' },
    JWT_AUDIENCE: { type: 'string' },
    JWT_ISSUER: { type: 'string' },
    JWT_SECRET: { type: 'string' },
    LLS_CDN_BASE_URL: { type: 'string' },
    LOG_LEVEL: {
      type: 'string',
      enum: ['trace', 'debug', 'info', 'warn', 'error', 'fatal'],
      default: 'trace',
    },
    LOGSTASH_HOST: { type: 'string', default: 'localhost' },
    LOGSTASH_PORT: { type: 'number', default: 5044 },
    MONGO_DB: { type: 'string' },
    MONGO_HOST: { type: 'string', default: 'localhost' },
    MONGO_ROOT_PASSWORD: { type: 'string' },
    MONGO_ROOT_USERNAME: { type: 'string' },
    MONGO_PORT: { type: 'number', default: 27017 },
    NODE_ENV: {
      type: 'string',
      enum: ['development', 'production', 'test'],
      default: 'development',
    },
    POSTGRES_DB: { type: 'string' },
    POSTGRES_HOST: { type: 'string', default: 'localhost' },
    POSTGRES_PASSWORD: { type: 'string' },
    POSTGRES_PORT: { type: 'number', default: 5432 },
    POSTGRES_USER: { type: 'string' },
    RATE_LIMIT_REDIS_CONNECT_TIMEOUT: { type: 'number', default: 500 },
    REDIS_HOST: { type: 'string', default: '127.0.0.1' },
    REDIS_PASSWORD: { type: 'string' },
    REDIS_PORT: { type: 'number', default: 6379 },
    SENTRY_DSN: { type: 'string' },
    SERVER_URL_DEV: { type: 'string' },
    SERVER_URL_PROD: { type: 'string' },
    SERVER_URL_STAGING: { type: 'string' },
    SWAGGER_UI_PATH: { type: 'string', default: '/api-docs' },
    TYPESENSE_API_KEY: { type: 'string' },
    TYPESENSE_HOST: { type: 'string', default: 'localhost' },
    TYPESENSE_PORT: { type: 'number', default: 8108 },
    TYPESENSE_PROTOCOL: { type: 'string', default: 'http' },
    WORKER_CONCURRENCY: { type: 'number', default: 50 },
  },
};

export default envSchema;
