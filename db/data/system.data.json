[{"themes": [{"field": "toastMessagePosition", "accessLevels": ["root"], "value": "bottom right"}, {"field": "alertBarPosition", "accessLevels": ["root"], "value": "top"}, {"field": "colorScheme", "accessLevels": ["organisation", "merchant"], "value": "#894AE0"}, {"field": "logo", "accessLevels": ["organisation", "merchant"], "value": ""}, {"field": "favicon", "accessLevels": ["organisation", "merchant"], "value": ""}]}, {"safety": [{"field": "sessionLifetimeHours", "accessLevels": ["root"], "value": 24}, {"field": "passwordExpiryDays", "accessLevels": ["root"], "value": 3}, {"field": "passwordReuseCount", "accessLevels": ["root"], "value": 5}, {"field": "passwordMaximumAttempts", "accessLevels": ["root"], "value": 5}, {"field": "twoFactorSessionTimeoutDays", "accessLevels": ["root"], "value": 7}, {"field": "twoFactorEnforcement", "accessLevels": ["organisation", "merchant"], "value": false}, {"field": "killSwitch", "accessLevels": ["organisation", "merchant"], "value": false}, {"field": "underAttackMode", "accessLevels": ["merchant"], "value": false}, {"field": "passwordMinimumLength", "accessLevels": ["organisation", "merchant"], "value": 8}, {"field": "passwordRequiredUppercase", "accessLevels": ["organisation", "merchant"], "value": true}, {"field": "passwordRequiredLowercase", "accessLevels": ["organisation", "merchant"], "value": true}, {"field": "passwordRequiredNumber", "accessLevels": ["organisation", "merchant"], "value": true}, {"field": "passwordRequiredSpecial", "accessLevels": ["organisation", "merchant"], "value": true}, {"field": "accessControl", "accessLevels": ["organisation", "merchant"], "value": true}, {"field": "usernameMasking", "accessLevels": ["merchant"], "value": false}, {"field": "emailMasking", "accessLevels": ["merchant"], "value": 0}, {"field": "mobileMasking", "accessLevels": ["merchant"], "currency": true, "value": 3}, {"field": "mobileMaskingStart", "accessLevels": ["merchant"], "currency": true, "value": 3}, {"field": "mobileMaskingEnd", "accessLevels": ["merchant"], "currency": true, "value": 6}]}, {"personal": [{"field": "appearance", "accessLevels": ["root", "organisation", "merchant"], "value": "system"}, {"field": "recordPerPage", "accessLevels": ["root", "organisation", "merchant"], "value": 25}, {"field": "dateFormat", "accessLevels": ["root", "organisation", "merchant"], "value": "yyyy-mm-dd"}, {"field": "timeFormat", "accessLevels": ["root", "organisation", "merchant"], "value": "24"}, {"field": "defaultLanguage", "accessLevels": ["root", "organisation", "merchant"], "value": ""}, {"field": "defaultTimezone", "accessLevels": ["root", "organisation", "merchant"], "value": ""}]}, {"general": [{"field": "lowCreditThreshold1", "accessLevels": ["merchant"], "value": 5000.0}, {"field": "lowCreditThreshold2", "accessLevels": ["merchant"], "value": 1000.0}, {"field": "language", "accessLevels": ["organisation", "merchant"], "value": ""}, {"field": "timezone", "accessLevels": ["organisation", "merchant"], "value": ""}]}]