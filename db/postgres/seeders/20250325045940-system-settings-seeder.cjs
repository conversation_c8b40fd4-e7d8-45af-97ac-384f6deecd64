'use strict';

const systemSettings = require('../../data/system.data.json');

const rootEntityId = 'beb9aa2c-09fd-11f0-ba09-df67e743a9a1';
const rootUserId = '9524c60a-3ab7-11f0-9fe2-0242ac120002';
const organisationEntityId = '1c9cd880-3c3d-11f0-982f-cfb9ea828c55';
const merchantEntityId = 'b3990d46-3c40-11f0-83bf-d7f9ebb5d708';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('Starting System Settings Seeder...');

      const { default: SettingModel } = await import(
        '#src/modules/core/models/postgres/setting.model.js'
      );
      const { default: CustomSettingModel } = await import(
        '#src/modules/core/models/postgres/custom-setting.model.js'
      );
      const { default: LocalisationModel } = await import(
        '#src/modules/core/models/postgres/localisation.model.js'
      );

      const settingModel = SettingModel({ psql: { connection: queryInterface.sequelize } });
      const customSettingModel = CustomSettingModel({
        psql: { connection: queryInterface.sequelize },
      });
      const localisationModel = LocalisationModel({
        psql: { connection: queryInterface.sequelize },
      });

      const seederData = systemSettings.flatMap((setting) => {
        const category = Object.keys(setting)[0];
        const data = setting[category];

        return data.map((item) => ({
          category,
          field: item.field,
          accessLevels: item.accessLevels,
          value: item.value,
          currency: item.currency,
        }));
      });

      const createdSettings = await settingModel.bulkCreate(seederData, {
        updateOnDuplicate: ['accessLevels'],
        transaction,
      });

      // Get default references
      const [defaultLang, defaultTimezone] = await Promise.all([
        localisationModel.findOne({ where: { category: 'language', code: 'EN' }, transaction }),
        localisationModel.findOne({ where: { category: 'region', code: 'MY' }, transaction }),
      ]);

      const currencies = await localisationModel.findAll({
        where: { category: 'currency' },
        transaction,
      });

      const customSettingData = [];
      for (const setting of createdSettings) {
        const categoryData = seederData.find(
          (data) => data.category === setting.category && data.field === setting.field,
        );

        let defaultValue;
        switch (setting.field) {
          case 'defaultLanguage':
            defaultValue = defaultLang?.id;
            break;
          case 'defaultTimezone':
            defaultValue = defaultTimezone?.id;
            break;
          default:
            defaultValue = categoryData.value;
        }

        const data = {
          parentId: setting.id,
          value: defaultValue,
        };

        await constructInsertData(customSettingData, data, categoryData, currencies);
      }

      await customSettingModel.bulkCreate(customSettingData, {
        ignoreDuplicates: true,
        transaction,
      });

      await transaction.commit();
      console.log('System Settings Seeder completed successfully.');
    } catch (error) {
      await transaction.rollback();
      console.error('Error in System Settings Seeder:', error);
      throw error;
    }
  },

  async down(queryInterface) {
    // Implement rollback if needed
  },
};

const constructInsertData = async (customSettingData, data, categoryData, currencies) => {
  for (const accessLevel of categoryData.accessLevels) {
    let entityId = '';
    if (categoryData.category === 'personal') {
      entityId = rootUserId;
    } else {
      switch (accessLevel) {
        case 'root':
          entityId = rootEntityId;
          break;
        case 'organisation':
          entityId = organisationEntityId;
          break;
        case 'merchant':
          entityId = merchantEntityId;
          break;
      }
    }

    if (categoryData.currency) {
      for (const currency of currencies) {
        customSettingData.push({
          ...data,
          entityId: entityId,
          localisationId: currency.id,
        });
      }
    } else {
      customSettingData.push({
        ...data,
        entityId: entityId,
      });
    }
  }
};
