import { DataTypes } from 'sequelize';

import { addBaseColumns } from '../helpers/baseColumns.cjs';

const CREDIT_LIMIT_TRANSACTIONS = {
  table: 'credit_limit_transactions',
  columns: addBaseColumns({
    entity_id: {
      allowNull: false,
      type: DataTypes.UUID,
      // remove comment when entities table is ready
      // references: {
      //   model: 'entities',
      //   key: 'id',
      // },
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
      comment: 'Reference to the merchant in entities table',
    },
    reference_id: {
      allowNull: true,
      type: DataTypes.UUID,
      comment: 'The id of reference table depends on the transaction type',
    },
    type: {
      allowNull: false,
      type: 'enum_credit_limit_transaction_type',
      comment: 'The type of this credit transaction',
    },
    base_amount: {
      allowNull: false,
      type: DataTypes.DECIMAL(30, 8),
      comment: 'The amount in member currency before convert USD',
    },
    exchange_rate: {
      allowNull: false,
      type: DataTypes.DECIMAL(15, 6),
      comment: 'The exchange rate used to convert USD',
    },
    amount: {
      allowNull: false,
      type: DataTypes.DECIMAL(30, 8),
      comment: 'The amount after convert USD',
    },
    before_balance: {
      allowNull: false,
      type: DataTypes.DECIMAL(30, 8),
      defaultValue: 0,
      comment: 'The balance before the transaction is applied',
    },
    after_balance: {
      allowNull: false,
      type: DataTypes.DECIMAL(30, 8),
      defaultValue: 0,
      comment: 'The balance after the transaction is applied',
    },
    status: {
      allowNull: false,
      type: 'enum_transaction_status',
      comment: 'The status of this credit transaction',
    },
  }),
  constraints: [],
  indexes: [
    {
      fields: ['entity_id', 'created_at'],
      options: {
        name: 'idx_credit_limit_transactions_entity_time',
        comment: 'Improves query performance when filtering by entity and timestamp',
      },
    },
    {
      fields: ['reference_id'],
      options: {
        name: 'idx_credit_limit_transactions_reference',
        comment: 'Improves query performance when join table',
      },
    },
    {
      fields: ['type'],
      options: {
        name: 'idx_credit_limit_transactions_type',
        comment: 'Improves query performance when filtering by type',
      },
    },
  ],
};

export default CREDIT_LIMIT_TRANSACTIONS;
