'use strict';

const { DataTypes } = require('sequelize');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await addColumn(queryInterface);

    await queryInterface.sequelize.query(`
      ALTER TYPE enum_bulk_job_type ADD VALUE IF NOT EXISTS 'create';
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('bulk_jobs', 'progress');

    // enum cannot remove, hence no drop command
  },
};

const addColumn = async (queryInterface) => {
  await queryInterface.addColumn('bulk_jobs', 'progress', {
    allowNull: true,
    type: DataTypes.DECIMAL(5, 2),
    defaultValue: 0,
    comment: 'To track the progress of the job',
  });
};
