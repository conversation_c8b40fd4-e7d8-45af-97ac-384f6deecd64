{"name": "wlsapi", "version": "0.0.7", "engines": {"node": ">=20.0.0"}, "type": "module", "description": "This project was bootstrapped with Fastify-CLI.", "main": "app.js", "repository": {"type": "git", "url": "https://bitbucket.org/aiodintech/wlsapi.git"}, "directories": {"test": "test"}, "scripts": {"check-node-version": "node -v | grep -q '^v20' || (echo 'Error: Node.js 20.x is required' && exit 1)", "dev": "fastify start -w -l info -P src/app.js --options", "docker:build": "docker compose build --no-cache", "docker:clean": "docker compose down --volumes --remove-orphans", "docker:down": "docker compose down", "docker:logs": "docker compose logs -f", "docker:rebuild": "docker compose down && docker compose build --no-cache && docker compose up -d", "docker:up": "docker compose up -d", "format": "prettier --config ./config/prettier.config.js  --write .", "lint-fix": "eslint  --config ./config/eslint.config.js . --fix", "lint": "eslint --config ./config/eslint.config.js . --max-warnings=10 && echo '✔ Linting complete. No issues found!'", "migrate:create": "npx sequelize-cli migration:generate --name", "migrate:rollback": "npx sequelize-cli db:migrate:undo", "migrate": "npx sequelize-cli db:migrate", "prepare": "husky", "seed:create": "npx sequelize-cli seed:generate --name", "seed:one": "npx sequelize-cli db:seed --seed", "start": "fastify start -l info src/app.js --options", "test": "npm run tu", "tu": "npx vitest --config ./config/vitest.config.js ./test/unit --coverage --watch=false", "version": "auto-changelog -p --starting-version v$npm_package_version --ending-version v$npm_package_version && git add CHANGELOG.md"}, "imports": {"#src/*": "./src/*", "#config/*": "./config/*", "#db/*": "./db/*"}, "keywords": [], "author": "Quantum Play", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.688.0", "@fastify/autoload": "^6.0.0", "@fastify/cors": "^10.0.1", "@fastify/env": "^5.0.1", "@fastify/helmet": "^12.0.1", "@fastify/jwt": "^9.1.0", "@fastify/multipart": "^9.0.1", "@fastify/rate-limit": "^10.1.1", "@fastify/redis": "^7.0.1", "@fastify/sensible": "^6.0.0", "@fastify/swagger": "^9.2.0", "@fastify/swagger-ui": "^5.1.0", "@sentry/node": "^8.37.1", "@sentry/profiling-node": "^8.39.0", "bignumber.js": "^9.3.0", "bullmq": "^5.52.2", "dompurify": "^3.2.5", "dotenv": "^16.4.5", "fastify": "^5.0.0", "fastify-cli": "^7.0.1", "fastify-plugin": "^5.0.0", "i18next": "^23.16.8", "i18next-http-backend": "^3.0.1", "ioredis": "^5.4.1", "jsdom": "^26.1.0", "lodash": "^4.17.21", "mongoose": "^8.8.1", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "pino-roll": "^2.2.0", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "typesense": "^1.8.2"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@eslint/js": "^9.13.0", "@vitest/coverage-v8": "^2.1.4", "@vitest/ui": "^2.1.4", "auto-changelog": "^2.5.0", "depcheck": "^1.4.7", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-sonarjs": "^3.0.0", "globals": "^15.11.0", "husky": "^9.1.6", "jest-diff": "^29.7.0", "lint-staged": "^15.2.10", "pino-pretty": "^11.3.0", "prettier": "^3.3.3", "vitest": "^2.1.4"}, "lint-staged": {"*": ["eslint --config ./config/eslint.config.js --fix", "prettier --config ./config/prettier.config.js  --write"], "*.js": "vitest run --config ./config/vitest.config.js --"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "./config/changelog-template.hbs", "unreleased": false, "commitLimit": false, "hideCredit": true, "replaceText": {"([bB]reaking change:)": "", "([bB]reaking:)": "", "(^[cC]hore:)": "", "(^[cC]hore\\(.+?\\):)": "", "(^[dD]ocs:)": "", "(^[dD]ocs\\(.+?\\):)": "", "(^[fF]eat:)": "", "(^[fF]eat\\(.+?\\):)": "", "(^[fF]eature/QPLY-[0-9]+\\s)": "", "(^[fF]ix:)": "", "(^[fF]ix\\(.+?\\):)": "", "(^[hH]otfix/QPLY-[0-9]+\\s)": "", "(^[pP]atch/QPLY-[0-9]+\\s)": "", "(^[pP]erf:)": "", "(^[pP]erf\\(.+?\\):)": "", "(^[rR]efactor:)": "", "(^[rR]efactor\\(.+?\\):)": "", "(^[sS]tyle:)": "", "(^[sS]tyle\\(.+?\\):)": "", "(^[tT]est:)": "", "(^[tT]est\\(.+?\\):)": ""}}}