import Redis from 'ioredis';

export default {
  rateLimitGroups: {
    // The timeWindow is specified in milliseconds (1 minute = 60000 milliseconds)
    default: {
      max: 100,
      timeWindow: 60000,
      name: 'default',
      descrption: 'Default rate limit for all routes.',
    },
    bo: { max: 10, timeWindow: 60000, name: 'bo', description: 'Rate limit for all BO routes.' },
  },
  groupPrefixes: {
    bo: ['/api/bo'],
  },
  whitelist: ['127.0.0.1'],
  addHeadersOnExceeding: {
    'X-RateLimit-Limit': true,
    'X-RateLimit-Remaining': true,
    'X-RateLimit-Reset': true,
  },
  addHeaders: {
    'X-RateLimit-Limit': true,
    'X-RateLimit-Remaining': true,
    'X-RateLimit-Reset': true,
    'Retry-After': true,
  },
  errorResponseBuilder: (request, context) => ({
    statusCode: 429,
    error: 'Too Many Requests',
    message: `Rate limit exceeded, retry in ${context.after}`,
  }),
  skipOnError: true,
  nameSpace: `${process.env.APP_NAME || 'default-app'}-rate-limit:`,
  redis: new Redis({
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
    connectTimeout: Number(process.env.RATE_LIMIT_REDIS_CONNECT_TIMEOUT || 500),
    maxRetriesPerRequest: 1,
    db: 1, // Using Redis DB 1 for rate limiting.
  }),
  cache: 10000,
  hook: 'onRequest',
};
