const queueNames = [/*'general-queue',*/ 'entity-queue'];

const completeRetention = {
  age: 3600, // keep up to 1 hour
  count: 10, // keep up to 1000 jobs
};

const failRetention = {
  age: 24 * 3600, // keep up to 24 hours
};

const baseConfig = {
  host: process.env.REDIS_HOST,
  password: process.env.REDIS_PASSWORD,
  port: process.env.REDIS_PORT,
  db: 3,
};

const workerConcurrency = Number(process.env.WORKER_CONCURRENCY);

export { queueNames, completeRetention, failRetention, baseConfig, workerConcurrency };
