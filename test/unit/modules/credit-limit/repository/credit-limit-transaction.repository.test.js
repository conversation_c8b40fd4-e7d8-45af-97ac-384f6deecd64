import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  CreditLimitConstant,
  CreditLimitTransactionRepository,
} from '#src/modules/credit-limit/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';

describe('Credit Limit Transaction Repository', () => {
  describe('create', () => {
    let mockFastify;
    const mockData = {
      entity_id: '00000000-0000-0000-0000-000000000001',
      reference_id: '00000000-0000-0000-0000-000000000002',
      type: CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES.CREDIT,
      base_amount: 40.0,
      exchange_rate: 4.0,
      amount: 10.0,
      status: CoreConstant.COMMON_TRANSACTION_STATUSES.PENDING,
    };
    const mockSchema = 'test';
    const mockOptions = { transaction: 'mockTransaction' };

    beforeEach(() => {
      vi.resetAllMocks();

      mockFastify = {
        psql: {
          CreditLimitTransaction: {
            schema: vi.fn().mockImplementation(() => {
              return {
                create: vi.fn().mockResolvedValue({}),
              };
            }),
          },
        },
      };
    });

    it('should call create with correct parameters', async () => {
      await CreditLimitTransactionRepository.create(mockFastify, mockData, mockSchema, mockOptions);

      expect(mockFastify.psql.CreditLimitTransaction.schema).toHaveBeenCalledWith(mockSchema);

      const schema = mockFastify.psql.CreditLimitTransaction.schema.mock.results[0].value;
      expect(schema.create).toHaveBeenCalledWith(mockData, mockOptions);
    });
  });

  describe('updateByEntityId', () => {
    let mockModelData;
    const mockUpdateData = {
      status: CoreConstant.COMMON_TRANSACTION_STATUSES.COMPLETED,
      updated_at: new Date(),
    };
    const mockOptions = { transaction: 'mockTransaction' };

    beforeEach(() => {
      vi.resetAllMocks();

      mockModelData = {
        update: vi.fn().mockResolvedValue({}),
      };
    });

    it('should call update with correct parameters', async () => {
      await CreditLimitTransactionRepository.update(mockModelData, mockUpdateData, mockOptions);

      expect(mockModelData.update).toHaveBeenCalledWith(mockUpdateData, mockOptions);
    });
  });
});
