import { beforeEach, describe, expect, it, vi } from 'vitest';
import { CreditLimitRepository } from '#src/modules/credit-limit/index.js';

describe('Credit Limit Repository', () => {
  describe('findByEntityId', () => {
    let mockFastify;
    const mockEntityId = '00000000-0000-0000-0000-000000000001';

    beforeEach(() => {
      vi.resetAllMocks();

      mockFastify = {
        psql: {
          CreditLimit: {
            findOne: vi.fn().mockResolvedValue({}),
          },
        },
      };
    });

    it('should call findOne with correct parameters', async () => {
      await CreditLimitRepository.findByEntityId(mockFastify, mockEntityId);

      expect(mockFastify.psql.CreditLimit.findOne).toHaveBeenCalledWith({
        where: {
          entity_id: mockEntityId,
        },
      });
    });
  });

  describe('updateByEntityId', () => {
    let mockModelData;

    const mockUpdateData = {
      balance: 1500,
      updated_at: new Date(),
      version: 1,
    };

    beforeEach(() => {
      vi.resetAllMocks();

      mockModelData = {
        update: vi.fn().mockResolvedValue({}),
      };
    });

    it('should call update with correct parameters', async () => {
      await CreditLimitRepository.updateByEntityId(mockModelData, mockUpdateData);

      expect(mockModelData.update).toHaveBeenCalledWith(mockUpdateData, {});
    });

    it('should call update with options if provided', async () => {
      const mockOptions = {
        transaction: 'mock transaction',
      };

      await CreditLimitRepository.updateByEntityId(mockModelData, mockUpdateData, mockOptions);

      expect(mockModelData.update).toHaveBeenCalledWith(mockUpdateData, mockOptions);
    });
  });
});
