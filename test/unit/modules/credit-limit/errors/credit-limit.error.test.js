import { describe, expect, it } from 'vitest';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { creditLimitError } from '#src/modules/credit-limit/errors/credit-limit.error.js';

describe('Credit Limit Error Module', () => {
  it('should create insufficientBalance error with correct properties', () => {
    const transactionId = 'uuid';
    const error = creditLimitError.insufficientBalance(transactionId);

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('70008');
    expect(error.message).toBe(
      `Insufficient credit balance to complete the transaction with ID: ${transactionId}`,
    );
    expect(error.statusCode).toBe(400);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.CREDIT_LIMIT}ModuleError`);
  });

  it('should create updateFail error with correct properties', () => {
    const transactionId = 'uuid';
    const error = creditLimitError.updateFail(transactionId);

    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('70009');
    expect(error.message).toBe(`Failed to update credit limit with ID: ${transactionId}`);
    expect(error.statusCode).toBe(400);
    expect(error.name).toBe(`${CoreConstant.MODULE_NAMES.CREDIT_LIMIT}ModuleError`);
  });
});
