import { beforeEach, describe, expect, it, vi } from 'vitest';
import { BigNumber } from 'bignumber.js';

import {
  CreditLimitRepository,
  CreditLimitTransactionRepository,
} from '#src/modules/credit-limit/repository/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CoreError } from '#src/modules/core/errors/index.js';
import { CreditLimitConstant } from '#src/modules/credit-limit/constants/index.js';
import { CreditLimitError } from '#src/modules/credit-limit/errors/index.js';
import { CreditLimitTransactionService } from '#src/modules/credit-limit/index.js';

vi.mock('#src/modules/credit-limit/repository/index.js');

describe('Credit Limit Service', () => {
  let mockFastify = {};
  const mockEntity = {
    id: '00000000-0000-0000-0000-000000000001',
    code: 'test',
  };
  const mockBaseAmount = 40.0;
  const mockExchangeRate = 4.0;
  const mockCreatedBy = 'user-uuid';
  const mockReferenceId = '00000000-0000-0000-0000-000000000002';

  const mockCreditLimit = { credit: 100.0, version: 1 };
  const amount = new BigNumber(mockBaseAmount)
    .dividedBy(mockExchangeRate)
    .decimalPlaces(8)
    .toString();
  const creditBalance = new BigNumber(mockCreditLimit.credit);
  const newBalance = creditBalance.plus(amount).toString();
  const mockCreditLimitTransaction = {
    id: '00000000-0000-0000-0000-000000000003',
  };

  beforeEach(() => {
    vi.resetAllMocks();

    mockFastify = {
      queue: {
        add: vi.fn(),
      },
    };
    CreditLimitRepository.findByEntityId.mockResolvedValue(mockCreditLimit);
    CreditLimitRepository.updateByEntityId.mockResolvedValue({
      ...mockCreditLimit,
      version: mockCreditLimit.version + 1,
    });
    CreditLimitTransactionRepository.create.mockResolvedValue(mockCreditLimitTransaction);
    CreditLimitTransactionRepository.update.mockResolvedValue(mockCreditLimitTransaction);
  });

  describe('createTransaction', () => {
    it('should throw invalidExRate error if exchange rate is 0 or negative', async () => {
      const invalidExRate = 0;
      const error = CreditLimitError.invalidExRate(invalidExRate);

      await expect(
        CreditLimitTransactionService.createTransaction(
          mockFastify,
          mockEntity,
          mockBaseAmount,
          invalidExRate,
          mockCreatedBy,
          mockReferenceId,
        ),
      ).rejects.toThrow(error);
    });

    it('should create a pending status, CREDIT type transaction when amount is > 0', async () => {
      const mockCreateData = {
        entityId: mockEntity.id,
        baseAmount: mockBaseAmount,
        exchangeRate: mockExchangeRate,
        type: CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES.CREDIT,
        referenceId: mockReferenceId,
        amount: amount,
        status: CoreConstant.COMMON_TRANSACTION_STATUSES.PENDING,
      };

      await CreditLimitTransactionService.createTransaction(
        mockFastify,
        mockEntity,
        mockBaseAmount,
        mockExchangeRate,
        mockCreatedBy,
        mockReferenceId,
      );

      expect(CreditLimitTransactionRepository.create).toHaveBeenCalledWith(
        mockFastify,
        mockCreateData,
        mockEntity.code,
        { authInfoId: mockCreatedBy },
      );
    });

    it('should create a pending status, DEBIT type transaction when amount is < 0', async () => {
      const mockCreateData = {
        entityId: mockEntity.id,
        baseAmount: -mockBaseAmount,
        exchangeRate: mockExchangeRate,
        type: CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES.DEBIT,
        referenceId: mockReferenceId,
        amount: `-${amount}`,
        status: CoreConstant.COMMON_TRANSACTION_STATUSES.PENDING,
      };

      await CreditLimitTransactionService.createTransaction(
        mockFastify,
        mockEntity,
        -mockBaseAmount,
        mockExchangeRate,
        mockCreatedBy,
        mockReferenceId,
      );

      expect(CreditLimitTransactionRepository.create).toHaveBeenCalledWith(
        mockFastify,
        mockCreateData,
        mockEntity.code,
        { authInfoId: mockCreatedBy },
      );
    });

    it('should update transaction to fail and throw error when adjust credit failed', async () => {
      const invalidAmount = -1000;

      await expect(
        CreditLimitTransactionService.createTransaction(
          mockFastify,
          mockEntity,
          invalidAmount,
          mockExchangeRate,
          mockCreatedBy,
          mockReferenceId,
        ),
      ).rejects.toThrow(expect.any(Error));

      expect(CreditLimitTransactionRepository.update).toHaveBeenCalledWith(
        mockCreditLimitTransaction,
        {
          status: CoreConstant.COMMON_TRANSACTION_STATUSES.FAILED,
        },
        { authInfoId: mockCreatedBy },
      );
    });

    it('should update transaction to completed on successful balance update', async () => {
      await CreditLimitTransactionService.createTransaction(
        mockFastify,
        mockEntity,
        mockBaseAmount,
        mockExchangeRate,
        mockCreatedBy,
        mockReferenceId,
      );

      expect(CreditLimitTransactionRepository.update).toHaveBeenCalledWith(
        mockCreditLimitTransaction,
        {
          beforeBalance: creditBalance.toString(),
          afterBalance: newBalance,
          status: CoreConstant.COMMON_TRANSACTION_STATUSES.COMPLETED,
        },
        { authInfoId: mockCreatedBy },
      );
    });

    it('should call addCheckCreditLimitThresholdJob', async () => {
      await CreditLimitTransactionService.createTransaction(
        mockFastify,
        mockEntity,
        mockBaseAmount,
        mockExchangeRate,
        mockCreatedBy,
        mockReferenceId,
      );

      // Can't mock internal function: addCheckCreditLimitThresholdJob
      // Using mockFastify.queue.add as indicator instead
      expect(mockFastify.queue.add).toHaveBeenCalledOnce();
    });
  });

  describe('adjustCredit', () => {
    it('should throw insufficientBalance error when deducting with insufficient funds', async () => {
      const negativeAmount = -1000;
      const error = CreditLimitError.insufficientBalance(mockCreditLimitTransaction.id);

      await expect(
        CreditLimitTransactionService.exportForUnitTest.adjustCredit(
          mockFastify,
          mockEntity.id,
          negativeAmount,
          false,
          mockCreditLimitTransaction.id,
        ),
      ).rejects.toThrow(error);
    });

    it('should allow negative balance when allowNegative is true', async () => {
      const negativeAmount = -1000;

      await CreditLimitTransactionService.exportForUnitTest.adjustCredit(
        mockFastify,
        mockEntity.id,
        negativeAmount,
        true, // allow negative balance
        mockCreditLimitTransaction.id,
      );

      // Should not update transaction to failed
      expect(CreditLimitRepository.updateByEntityId).toHaveBeenCalledWith(mockCreditLimit, {
        credit: (mockCreditLimit.credit + negativeAmount).toString(),
        version: mockCreditLimit.version,
      });
    });

    it('should update credit limit with new balance', async () => {
      await CreditLimitTransactionService.exportForUnitTest.adjustCredit(
        mockFastify,
        mockEntity.id,
        amount,
        false,
        mockCreditLimitTransaction.id,
      );

      expect(CreditLimitRepository.updateByEntityId).toHaveBeenCalledWith(mockCreditLimit, {
        credit: newBalance,
        version: mockCreditLimit.version,
      });
    });

    it('should return before and new balance when update success', async () => {
      const result = await CreditLimitTransactionService.exportForUnitTest.adjustCredit(
        mockFastify,
        mockEntity.id,
        amount,
        false,
        mockCreditLimitTransaction.id,
      );

      expect(result).toEqual({
        beforeBalance: creditBalance.toString(),
        newBalance,
      });
    });

    it('should retry on update failure', async () => {
      // Mock update to fail first, then succeed
      CreditLimitRepository.updateByEntityId
        .mockImplementationOnce(() => {
          throw CoreError.versionConflict(0);
        })
        .mockResolvedValueOnce({ id: 1 }); // Success on second try

      await CreditLimitTransactionService.exportForUnitTest.adjustCredit(
        mockFastify,
        mockEntity.id,
        amount,
        false,
        mockCreditLimitTransaction.id,
      );

      // Should have been called twice (one failure, one success)
      expect(CreditLimitRepository.updateByEntityId).toHaveBeenCalledTimes(2);
    });

    it('should retrieve entity credit limit again on every retry', async () => {
      // Mock update to fail first, then succeed
      CreditLimitRepository.updateByEntityId
        .mockImplementationOnce(() => {
          throw CoreError.versionConflict(0);
        })
        .mockResolvedValueOnce({ id: 1 }); // Success on second try

      await CreditLimitTransactionService.exportForUnitTest.adjustCredit(
        mockFastify,
        mockEntity.id,
        amount,
        false,
        mockCreditLimitTransaction.id,
      );

      // Should have been called twice (one failure, one success)
      expect(CreditLimitRepository.findByEntityId).toHaveBeenCalledTimes(2);
    });

    it('should throw updateFail error when all OPTIMISTIC_RETRY update attempts fail', async () => {
      // Mock all update attempts to fail
      CreditLimitRepository.updateByEntityId.mockImplementation(() => {
        throw CoreError.versionConflict(0);
      });

      const error = CreditLimitError.updateFail(mockCreditLimitTransaction.id);

      await expect(
        CreditLimitTransactionService.exportForUnitTest.adjustCredit(
          mockFastify,
          mockEntity.id,
          amount,
          false,
          mockCreditLimitTransaction.id,
        ),
      ).rejects.toThrow(error);

      // Should have been called the N times of OPTIMISTIC_RETRY
      expect(CreditLimitRepository.updateByEntityId).toHaveBeenCalledTimes(
        CreditLimitConstant.OPTIMISTIC_RETRY,
      );
    });

    it('should not retry if encounter error other than versionConflict error', async () => {
      const error = new Error('Other error');
      CreditLimitRepository.updateByEntityId.mockImplementationOnce(() => {
        throw error;
      });

      await expect(
        CreditLimitTransactionService.exportForUnitTest.adjustCredit(
          mockFastify,
          mockEntity.id,
          amount,
          false,
          mockCreditLimitTransaction.id,
        ),
      ).rejects.toThrow(error);

      // Should have been called once and fail directly
      expect(CreditLimitRepository.updateByEntityId).toHaveBeenCalledTimes(1);
    });
  });

  describe('checkCreditLimitThreshold', () => {
    it('should add a job to check credit limit threshold', async () => {
      await CreditLimitTransactionService.exportForUnitTest.addCheckCreditLimitThresholdJob(
        mockFastify,
        mockEntity.id,
        newBalance,
      );

      expect(mockFastify.queue.add).toHaveBeenCalledWith('entity-queue', 'check-balance', {
        entityId: mockEntity.id,
        creditLimit: newBalance,
      });
    });
  });
});
