import { beforeEach, describe, expect, it, vi } from 'vitest';

import { getCache, setCache } from '#src/utils/cache.util.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

import * as EntityModule from '#src/modules/entity/index.js';
import {
  LocalisationConstant,
  LocalisationRepository,
  SettingRepository,
} from '#src/modules/setting/index.js';
import {
  checkCreditLimitThreshold,
  createMerchantJob,
  exportForUnitTest,
} from '#src/modules/core/workers/entity.worker.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CreditLimitTransactionService } from '#src/modules/credit-limit/index.js';

vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/db-transaction.util.js');
vi.mock('#src/modules/setting/index.js');

const {
  addCopySetting,
  assignCreditLimit,
  copyMerchantSetting,
  getScaffoldTableList,
  scaffoldTable,
  shouldCopyMerchantSetting,
} = exportForUnitTest;

describe('Entity Worker', () => {
  let mockFastify = {};

  describe('getScaffoldTableList', () => {
    it('should return an array of table templates', () => {
      const result = getScaffoldTableList();

      // Check that the result is an array
      expect(Array.isArray(result)).toEqual(true);
    });

    it('should include all required table properties in each template', () => {
      const result = getScaffoldTableList();

      // Check each table template has the required properties
      result.forEach((template) => {
        expect(template).toHaveProperty('table');
        expect(template).toHaveProperty('columns');
        expect(template).toHaveProperty('constraints');
        expect(template).toHaveProperty('indexes');
      });
    });
  });

  describe('scaffoldTable', () => {
    const mockSchema = 'test';
    let mockQueryInterface = {};
    const mockTransaction = 'mockTransaction';
    const mockGetScaffoldTableList = [
      {
        table: 'template1',
        columns: {
          member_id: {},
        },
        constraints: [{}],
        indexes: [
          {
            fields: [],
            options: {},
          },
        ],
      },
    ];

    beforeEach(() => {
      vi.resetAllMocks();

      mockQueryInterface = {
        createTable: vi.fn().mockResolvedValue(undefined),
        addConstraint: vi.fn().mockResolvedValue(undefined),
        addIndex: vi.fn().mockResolvedValue(undefined),
      };

      mockFastify = {
        psql: {
          connection: {
            createSchema: vi.fn(),
            getQueryInterface: vi.fn().mockReturnValue(mockQueryInterface),
          },
        },
      };

      withTransaction.mockImplementation(async (fastify, options, callback) => {
        return await callback(mockTransaction);
      });
    });

    it('should create new schema based on supplied schema', async () => {
      await scaffoldTable(mockFastify, mockSchema);

      expect(mockFastify.psql.connection.createSchema).toHaveBeenCalledWith(mockSchema, {
        ifNotExist: true,
      });
    });

    it('should use transaction for DB operation', async () => {
      await scaffoldTable(mockFastify, mockSchema);

      expect(withTransaction).toHaveBeenCalled();
    });

    it('should create table in the defined schema with transaction', async () => {
      await scaffoldTable(mockFastify, mockSchema, mockGetScaffoldTableList);

      expect(mockQueryInterface.createTable).toHaveBeenCalledWith(
        mockGetScaffoldTableList[0].table,
        mockGetScaffoldTableList[0].columns,
        {
          schema: mockSchema,
          transaction: mockTransaction,
        },
      );
    });

    it('should add constraints to the created tables with transaction', async () => {
      await scaffoldTable(mockFastify, mockSchema, mockGetScaffoldTableList);

      expect(mockQueryInterface.addConstraint).toHaveBeenCalledWith(
        `${mockSchema}.${mockGetScaffoldTableList[0].table}`,
        {
          ...mockGetScaffoldTableList[0].constraints[0],
          transaction: mockTransaction,
        },
      );
    });

    it('should add indexes to the created tables with transaction', async () => {
      await scaffoldTable(mockFastify, mockSchema, mockGetScaffoldTableList);

      expect(mockQueryInterface.addIndex).toHaveBeenCalledWith(
        `${mockSchema}.${mockGetScaffoldTableList[0].table}`,
        mockGetScaffoldTableList[0].indexes[0].fields,
        {
          ...mockGetScaffoldTableList[0].indexes[0].options,
          transaction: mockTransaction,
        },
      );
    });

    it('should handle multiple template tables', async () => {
      // Mock implementation to test with multiple tables
      const mockGetScaffoldTableList = [
        { table: 'template1', columns: {}, constraints: [], indexes: [] },
        { table: 'template2', columns: {}, constraints: [], indexes: [] },
        { table: 'template3', columns: {}, constraints: [], indexes: [] },
      ];

      await scaffoldTable(mockFastify, mockSchema, mockGetScaffoldTableList);

      expect(mockQueryInterface.createTable).toHaveBeenCalledTimes(3);
    });
  });

  describe('assignCreditLimit', () => {
    const mockEntity = {
      id: 'uuid',
      code: 'test',
    };
    const mockCreatedBy = 'user-uuid';

    beforeEach(() => {
      vi.resetAllMocks();

      vi.spyOn(CreditLimitTransactionService, 'createTransaction');
    });

    it('should not set credit limit if creditLimit is less than or equal to 0', async () => {
      await assignCreditLimit(mockFastify, mockEntity, 0, mockCreatedBy);

      expect(CreditLimitTransactionService.createTransaction).not.toHaveBeenCalled();
    });

    it('should set credit limit if creditLimit is more than 0', async () => {
      await assignCreditLimit(mockFastify, mockEntity, 10, mockCreatedBy);

      expect(CreditLimitTransactionService.createTransaction).toHaveBeenCalledOnce();
      expect(CreditLimitTransactionService.createTransaction).toHaveBeenCalledWith(
        mockFastify,
        mockEntity,
        10,
        1,
        mockCreatedBy,
      );
    });
  });

  describe('shouldCopyMerchantSetting', () => {
    it('should return false when copyConfig.copy is false', () => {
      const copyConfig = {
        copy: false,
      };

      expect(shouldCopyMerchantSetting('language', copyConfig)).toEqual(false);
    });

    it('should return true when opyConfig.copy is true and copyConfig.option is all', () => {
      const copyConfig = {
        copy: true,
        option: 'all',
      };

      expect(shouldCopyMerchantSetting('language', copyConfig)).toEqual(true);
    });

    describe('when copy is true and option is "custom"', () => {
      let copyConfig = {};

      beforeEach(() => {
        vi.resetAllMocks();

        copyConfig = {
          copy: true,
          option: 'custom',
          setting: {
            language: true,
            timezone: false,
            colorScheme: true,
            twoFactorEnforcement: false,
            usernameMasking: true,
            emailMasking: false,
            accessControl: true,
            creditLimitAlert: true,
            passwordPolicy: false,
            mobileMasking: true,
          },
        };
      });

      it('should return correct value for direct settings', () => {
        expect(shouldCopyMerchantSetting('language', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('timezone', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('colorScheme', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('twoFactorEnforcement', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('usernameMasking', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('emailMasking', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('accessControl', copyConfig)).toEqual(true);
      });

      it('should return correct value for credit limit threshold settings', () => {
        expect(shouldCopyMerchantSetting('lowCreditThreshold1', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('lowCreditThreshold2', copyConfig)).toEqual(true);

        // Test with creditLimitAlert set to false
        copyConfig.setting.creditLimitAlert = false;
        expect(shouldCopyMerchantSetting('lowCreditThreshold1', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('lowCreditThreshold2', copyConfig)).toEqual(false);
      });

      it('should return correct value for password policy settings', () => {
        expect(shouldCopyMerchantSetting('passwordMinimumLength', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('passwordRequiredUppercase', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('passwordRequiredLowercase', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('passwordRequiredNumber', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('passwordRequiredSpecial', copyConfig)).toEqual(false);

        // Test with passwordPolicy set to true
        copyConfig.setting.passwordPolicy = true;
        expect(shouldCopyMerchantSetting('passwordMinimumLength', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('passwordRequiredUppercase', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('passwordRequiredLowercase', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('passwordRequiredNumber', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('passwordRequiredSpecial', copyConfig)).toEqual(true);
      });

      it('should return correct value for mobile masking settings', () => {
        expect(shouldCopyMerchantSetting('mobileMasking', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('mobileMaskingStart', copyConfig)).toEqual(true);
        expect(shouldCopyMerchantSetting('mobileMaskingEnd', copyConfig)).toEqual(true);

        // Test with mobileMasking set to false
        copyConfig.setting.mobileMasking = false;
        expect(shouldCopyMerchantSetting('mobileMasking', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('mobileMaskingStart', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('mobileMaskingEnd', copyConfig)).toEqual(false);
      });

      it('should return false for unknown fields', () => {
        expect(shouldCopyMerchantSetting('unknownField', copyConfig)).toEqual(false);
        expect(shouldCopyMerchantSetting('anotherUnknownField', copyConfig)).toEqual(false);
      });
    });
  });

  describe('addCopySetting', () => {
    let customSettingData = [];
    let setting = {};
    let customSetting = {};
    const entityId = 'entity-uuid';

    beforeEach(() => {
      vi.resetAllMocks();

      customSettingData = [];
      setting = { id: 'setting-uuid', field: 'lowCreditThreshold1' };
      customSetting = { value: 10000, localisationId: null };
    });

    it('should use default value when copy is false', () => {
      addCopySetting(customSettingData, setting, customSetting, entityId, false);

      expect(customSettingData[0].value).toEqual(
        EntityModule.EntityConstant.MERCHANT_SETTING_DEFAULT.lowCreditThreshold1,
      );
    });

    it('should use default value when copy is true', () => {
      addCopySetting(customSettingData, setting, customSetting, entityId, true);

      expect(customSettingData[0].value).toEqual(customSetting.value);
    });

    it('should push the data to customSettingData with correct format', () => {
      addCopySetting(customSettingData, setting, customSetting, entityId, true);

      expect(customSettingData).toEqual([
        {
          parentId: setting.id,
          value: customSetting.value,
          localisationId: customSetting.localisationId,
          entityId: entityId,
        },
      ]);
    });
  });

  describe('copyMerchantSetting', () => {
    const mockParams = {
      entity: {
        id: 'target-entity-uuid',
      },
      copyConfig: {
        entityId: 'source-entity-uuid',
        copy: true,
        option: 'all',
      },
      createdBy: 'user-uuid',
    };
    const mockSupportedCurrencies = [{ parentId: 'currency-1' }, { parentId: 'currency-2' }];
    const mockSourceSettings = [
      {
        id: 'setting-1',
        field: 'language',
        customSettings: [{ value: 'en', localisationId: null }],
      },
      {
        id: 'setting-2',
        field: 'mobileMasking',
        customSettings: [
          { value: 1, localisationId: 'currency-1' },
          { value: 2, localisationId: 'currency-2' },
          { value: 3, localisationId: 'currency-3' },
        ],
      },
      {
        id: 'setting-3',
        field: 'mobileMaskingStart',
        customSettings: [
          { value: 4, localisationId: 'currency-1' },
          { value: 5, localisationId: 'currency-2' },
          { value: 6, localisationId: 'currency-3' },
        ],
      },
      {
        id: 'setting-4',
        field: 'mobileMaskingEnd',
        customSettings: [
          { value: 7, localisationId: 'currency-1' },
          { value: 8, localisationId: 'currency-2' },
          { value: 9, localisationId: 'currency-3' },
        ],
      },
      {
        id: 'setting-5',
        field: 'accessControl',
        customSettings: [{ value: true, localisationId: null }],
      },
    ];
    const mockTransaction = 'mockTransaction';

    beforeEach(() => {
      vi.resetAllMocks();

      mockFastify = {};

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSourceSettings);
      SettingRepository.bulkCreate.mockResolvedValue([]);

      withTransaction.mockImplementation(async (fastify, options, callback) => {
        return await callback(mockTransaction);
      });

      vi.spyOn(EntityModule, 'EntityConstant', 'get').mockReturnValue({
        MERCHANT_SETTING_DEFAULT: {
          language: '',
        },
      });
    });

    it('should fetch source settings from the repository once', async () => {
      await copyMerchantSetting(mockFastify, mockParams, mockSupportedCurrencies);

      expect(SettingRepository.findByCategoryAndAccess).toHaveBeenCalledTimes(1);
      expect(SettingRepository.findByCategoryAndAccess).toHaveBeenCalledWith(mockFastify, {
        'filter_customSettings.entityId_eq': mockParams.copyConfig.entityId,
      });
    });

    it('should call bulkCreate with transaction', async () => {
      await copyMerchantSetting(mockFastify, mockParams, mockSupportedCurrencies);

      expect(withTransaction).toHaveBeenCalledOnce();
      expect(SettingRepository.bulkCreate).toBeCalledWith(mockFastify, expect.any(Array), {
        transaction: mockTransaction,
        authInfoId: expect.any(String),
      });
    });

    it('should call bulkCreate with params.createdBy', async () => {
      await copyMerchantSetting(mockFastify, mockParams, mockSupportedCurrencies);

      expect(SettingRepository.bulkCreate).toBeCalledWith(mockFastify, expect.any(Array), {
        transaction: expect.anything(),
        authInfoId: mockParams.createdBy,
      });
    });

    it('should not have localisationId for other settings', async () => {
      await copyMerchantSetting(mockFastify, mockParams, mockSupportedCurrencies);

      expect(SettingRepository.bulkCreate).toBeCalledWith(
        mockFastify,
        [
          {
            entityId: mockParams.entity.id,
            parentId: mockSourceSettings[0].id,
            value: mockSourceSettings[0].customSettings[0].value,
            localisationId: mockSourceSettings[0].customSettings[0].localisationId,
          },
        ],
        expect.any(Object),
      );
    });

    it('should have localisationId for mobileMasking settings', async () => {
      vi.spyOn(EntityModule, 'EntityConstant', 'get').mockReturnValueOnce({
        MERCHANT_SETTING_DEFAULT: {
          mobileMasking: 3,
          mobileMaskingStart: 3,
          mobileMaskingEnd: 6,
        },
      });

      await copyMerchantSetting(mockFastify, mockParams, mockSupportedCurrencies);

      expect(SettingRepository.bulkCreate).toBeCalledWith(
        mockFastify,
        [
          {
            entityId: mockParams.entity.id,
            parentId: mockSourceSettings[1].id,
            value: mockSourceSettings[1].customSettings[0].value,
            localisationId: mockSourceSettings[1].customSettings[0].localisationId,
          },
          {
            entityId: mockParams.entity.id,
            parentId: mockSourceSettings[1].id,
            value: mockSourceSettings[1].customSettings[1].value,
            localisationId: mockSourceSettings[1].customSettings[1].localisationId,
          },
          {
            entityId: mockParams.entity.id,
            parentId: mockSourceSettings[2].id,
            value: mockSourceSettings[2].customSettings[0].value,
            localisationId: mockSourceSettings[2].customSettings[0].localisationId,
          },
          {
            entityId: mockParams.entity.id,
            parentId: mockSourceSettings[2].id,
            value: mockSourceSettings[2].customSettings[1].value,
            localisationId: mockSourceSettings[2].customSettings[1].localisationId,
          },
          {
            entityId: mockParams.entity.id,
            parentId: mockSourceSettings[3].id,
            value: mockSourceSettings[3].customSettings[0].value,
            localisationId: mockSourceSettings[3].customSettings[0].localisationId,
          },
          {
            entityId: mockParams.entity.id,
            parentId: mockSourceSettings[3].id,
            value: mockSourceSettings[3].customSettings[1].value,
            localisationId: mockSourceSettings[3].customSettings[1].localisationId,
          },
        ],
        expect.any(Object),
      );
    });

    it('should not include mobileMasking setting with unsupported currency', async () => {
      vi.spyOn(EntityModule, 'EntityConstant', 'get').mockReturnValueOnce({
        MERCHANT_SETTING_DEFAULT: {
          mobileMasking: 3,
          mobileMaskingStart: 3,
          mobileMaskingEnd: 6,
        },
      });

      await copyMerchantSetting(mockFastify, mockParams, mockSupportedCurrencies);

      // Get all calls to bulkCreate
      const bulkCreateCalls = SettingRepository.bulkCreate.mock.calls;

      // Extract all items from all calls
      const allCreatedItems = bulkCreateCalls.flatMap((call) => call[1]);

      // Check that no item with the unsupported currency exists
      const unsupportedCurrencyItem = allCreatedItems.find(
        (item) => item.localisationId === 'currency-3',
      );

      expect(unsupportedCurrencyItem).toBeUndefined();
    });

    // add access control copy test case here
  });

  describe('createMerchantJob', () => {
    const mockParams = {
      entity: {
        id: 'entity-uuid',
        code: 'test',
      },
      creditLimit: 10000,
      copyConfig: {
        copy: true,
        entityId: 'source-entity-uuid',
        option: 'all',
      },
      createdBy: 'user-uuid',
    };
    const mockSupportedCurrencies = {
      rows: [{ parentId: 'currency-1' }, { parentId: 'currency-2' }],
    };
    let mockFunctions = {};

    beforeEach(() => {
      vi.resetAllMocks();

      mockFastify = {
        log: {
          error: vi.fn(),
        },
      };

      mockFunctions = {
        scaffoldTableFn: vi.fn(),
        assignCreditLimitFn: vi.fn(),
        copyMerchantSettingFn: vi.fn(),
      };

      // Mock LocalisationRepository
      LocalisationRepository.findAll.mockResolvedValue(mockSupportedCurrencies);
    });

    it('should call scaffoldTable with correct params', async () => {
      await createMerchantJob(mockFastify, mockParams, mockFunctions);

      expect(mockFunctions.scaffoldTableFn).toBeCalledWith(mockFastify, mockParams.entity.code);
    });

    it('should call assignCreditLimit with correct params', async () => {
      await createMerchantJob(mockFastify, mockParams, mockFunctions);

      expect(mockFunctions.assignCreditLimitFn).toBeCalledWith(
        mockFastify,
        mockParams.entity,
        mockParams.creditLimit,
        mockParams.createdBy,
      );
    });

    it('should get current merchant localisation with correct params', async () => {
      await createMerchantJob(mockFastify, mockParams, mockFunctions);

      expect(LocalisationRepository.findAll).toBeCalledWith(mockFastify, {
        'filter_localisation.category_eq': LocalisationConstant.LOCALISATION_CATEGORIES.CURRENCY,
        filter_entityId_eq: mockParams.entity.id,
        limit: 0,
      });
    });

    it('should call copyMerchantSetting with correct params', async () => {
      await createMerchantJob(mockFastify, mockParams, mockFunctions);

      expect(mockFunctions.copyMerchantSettingFn).toBeCalledWith(
        mockFastify,
        mockParams,
        mockSupportedCurrencies.rows,
      );
    });

    it('should throw error when encounter error', async () => {
      const error = new Error('mock error');
      mockFunctions.scaffoldTableFn.mockRejectedValueOnce(error);

      await expect(createMerchantJob(mockFastify, mockParams, mockFunctions)).rejects.toThrow(
        error,
      );
    });
  });

  describe('checkCreditLimitThreshold', () => {
    const mockSetting = [
      { field: 'lowCreditThreshold1', customSettings: { value: 5000 } },
      { field: 'lowCreditThreshold2', customSettings: { value: 1000 } },
    ];
    const mockEntityId = 'entity-uuid';
    const mockMerchantCode = 'test';

    beforeEach(() => {
      vi.resetAllMocks();

      mockFastify = {
        redis: {},
      };

      SettingRepository.findByCategoryAndAccess.mockResolvedValue(mockSetting);

      getCache.mockResolvedValue(true);
      setCache.mockResolvedValue();
    });

    it('should search system setting with correct params', async () => {
      await checkCreditLimitThreshold(mockFastify, {
        entityId: mockEntityId,
        creditLimit: 1000,
      });

      expect(SettingRepository.findByCategoryAndAccess).toBeCalledWith(mockFastify, {
        'filter_customSettings.entityId_eq': mockEntityId,
        filter_category_eq: 'general',
        filter_accessLevels_overlap: 'merchant',
      });
    });

    it('should get lowCreditThreshold2 cache when credit limit balance is lower than lowCreditThreshold2', async () => {
      const expectedCacheKey = `${mockMerchantCode}:credit-threshold2`;

      await checkCreditLimitThreshold(mockFastify, {
        entityId: mockEntityId,
        creditLimit: 999,
      });

      expect(getCache).toBeCalledWith(mockFastify.redis, expectedCacheKey);
    });

    it('should get lowCreditThreshold1 cache when credit limit balance is lower than lowCreditThreshold1 and higher than lowCreditThreshold1', async () => {
      const expectedCacheKey = `${mockMerchantCode}:credit-threshold1`;

      await checkCreditLimitThreshold(mockFastify, {
        entityId: mockEntityId,
        creditLimit: 4999,
      });

      expect(getCache).toBeCalledWith(mockFastify.redis, expectedCacheKey);
    });

    it('should not send alert when cache is found', async () => {
      getCache.mockResolvedValueOnce(true);

      await checkCreditLimitThreshold(mockFastify, {
        entityId: mockEntityId,
        creditLimit: 0,
      });

      expect(setCache).not.toBeCalled();
    });

    it('should set cache for 24 hours after sending alert', async () => {
      getCache.mockResolvedValueOnce(false);

      const cacheKey = `${mockMerchantCode}:credit-threshold2`;

      await checkCreditLimitThreshold(mockFastify, {
        entityId: mockEntityId,
        creditLimit: 0,
      });

      expect(setCache).toBeCalledWith(
        mockFastify.redis,
        cacheKey,
        true,
        CoreConstant.CACHE_SECOND.DAILY,
      );
    });
  });
});
