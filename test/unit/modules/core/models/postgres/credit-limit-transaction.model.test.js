import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { CreditLimitConstant } from '#src/modules/credit-limit/index.js';
import CreditLimitTransactionModel from '#src/modules/core/models/postgres/credit-limit-transaction.model.js';
import { auditableMixin } from '#src/mixins/index.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

describe('Credit Limit Transaction Model', () => {
  let mockFastify;
  let CreditLimitTransaction;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    Model.beforeUpdate = vi.fn();

    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    // Initialize the model
    CreditLimitTransaction = CreditLimitTransactionModel(mockFastify);
  });

  it('should return a model class that extends Sequelize.Model', () => {
    expect(CreditLimitTransaction.prototype).toBeInstanceOf(Model);
  });

  it('should apply auditable mixin to the model', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(CreditLimitTransaction);
  });

  describe('Model initialization', () => {
    it('should initialize the model with correct attributes', () => {
      expect(Model.init).toHaveBeenCalledWith(
        {
          id: {
            type: DataTypes.UUID,
            defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
            primaryKey: true,
          },
          entityId: {
            type: DataTypes.UUID,
            allowNull: false,
          },
          referenceId: {
            type: DataTypes.UUID,
            allowNull: true,
          },
          type: {
            type: DataTypes.ENUM(Object.values(CreditLimitConstant.CREDIT_LIMIT_TRANSACTION_TYPES)),
            allowNull: false,
          },
          baseAmount: {
            type: DataTypes.DECIMAL(30, 8),
            allowNull: false,
          },
          exchangeRate: {
            type: DataTypes.DECIMAL(30, 8),
            allowNull: false,
            validate: {
              min: 0,
            },
          },
          amount: {
            type: DataTypes.DECIMAL(30, 8),
            allowNull: false,
          },
          beforeBalance: {
            type: DataTypes.DECIMAL(30, 8),
            allowNull: false,
            defaultValue: 0,
          },
          afterBalance: {
            type: DataTypes.DECIMAL(30, 8),
            allowNull: false,
            defaultValue: 0,
          },
          status: {
            type: DataTypes.ENUM(Object.values(CoreConstant.COMMON_TRANSACTION_STATUSES)),
            allowNull: false,
          },
          createdBy: {
            type: DataTypes.UUID,
            allowNull: true,
          },
          updatedBy: {
            type: DataTypes.UUID,
            allowNull: true,
          },
        },
        {
          modelName: 'CreditLimitTransaction',
          sequelize: mockFastify.psql.connection,
          tableName: 'credit_limit_transactions',
          timestamps: true,
          underscored: true,
        },
      );
    });
  });
});
