import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CreditLimitModel from '#src/modules/core/models/postgres/credit-limit.model.js';
import { versionedMixin } from '#src/mixins/index.js';

vi.mock('#src/mixins/index.js', () => ({
  versionedMixin: {
    applyVersioning: vi.fn(),
  },
}));

describe('Credit Limit Model', () => {
  let mockFastify;
  let CreditLimit;

  beforeEach(() => {
    vi.resetAllMocks();

    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    Model.beforeUpdate = vi.fn();

    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };

    // Initialize the model
    CreditLimit = CreditLimitModel(mockFastify);
  });

  it('should return a model class that extends Sequelize.Model', () => {
    expect(CreditLimit.prototype).toBeInstanceOf(Model);
  });

  it('should apply versioning mixin to the model', () => {
    expect(versionedMixin.applyVersioning).toHaveBeenCalledWith(CreditLimit);
  });

  describe('Model initialization', () => {
    it('should initialize the model with correct attributes', () => {
      expect(Model.init).toHaveBeenCalledWith(
        {
          id: {
            type: DataTypes.UUID,
            defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
            primaryKey: true,
          },
          entityId: {
            type: DataTypes.UUID,
            allowNull: false,
          },
          credit: {
            type: DataTypes.DECIMAL(30, 8),
            allowNull: false,
          },
          version: {
            type: DataTypes.BIGINT,
            allowNull: false,
            defaultValue: 1,
            validate: {
              min: 1,
              notNull: { msg: 'version is required' },
            },
          },
        },
        {
          modelName: 'CreditLimit',
          sequelize: mockFastify.psql.connection,
          tableName: 'credit_limits',
          timestamps: true,
          underscored: true,
        },
      );
    });
  });
});
