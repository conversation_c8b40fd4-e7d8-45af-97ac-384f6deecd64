import { beforeEach, describe, expect, it, vi } from 'vitest';

import { DataTypes, Model, Sequelize } from 'sequelize';
import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import CustomSettingModel from '#src/modules/core/models/postgres/custom-setting.model.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
  versionedMixin: {
    applyVersioning: vi.fn(),
  },
}));

describe('CustomSetting Model', () => {
  let mockFastify;
  let CustomSetting;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.belongsTo = vi.fn();
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    // Initialize the model
    CustomSetting = CustomSettingModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      Setting: {},
    };
    CustomSetting.associate(mockModels);
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.Setting, {
      foreignKey: 'parentId',
      as: 'setting',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  it('should call auditableMixin.applyAuditFields and versionedMixin.applyVersioning', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(CustomSetting);
    expect(versionedMixin.applyVersioning).toHaveBeenCalledWith(CustomSetting);
  });

  describe('Model initialization', () => {
    let fields;
    let initOptions;

    beforeEach(() => {
      [fields, initOptions] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(initOptions.modelName).toBe('CustomSetting');
      expect(initOptions.tableName).toBe('custom_settings');
    });

    it('should set underscored and timestamps to true', () => {
      expect(initOptions.underscored).toBe(true);
      expect(initOptions.timestamps).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initOptions.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct index', () => {
      expect(initOptions.indexes).toEqual([
        {
          unique: true,
          fields: ['parent_id', 'entity_id', 'localisation_id'],
        },
      ]);
    });

    it('should define the correct fields', () => {
      expect(fields).toEqual({
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        parentId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        entityId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        localisationId: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        value: {
          type: DataTypes.STRING(100),
          allowNull: false,
          validate: {
            len: [1, 100],
            notEmpty: true,
          },
        },
        version: {
          type: DataTypes.BIGINT,
          allowNull: false,
          defaultValue: 1,
          validate: {
            min: 1,
            notNull: { msg: 'version is required' },
          },
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      });
    });
  });
});
