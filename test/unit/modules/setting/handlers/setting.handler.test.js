import { beforeEach, describe, expect, it, vi } from 'vitest';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import {
  index,
  options,
  updatePersonal,
  updateSafety,
  updateThemes,
} from '#src/modules/setting/handlers/setting.handler.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { SettingConstant } from '#src/modules/setting/constants/index.js';
import { SettingService } from '#src/modules/setting/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

// Mock dependencies
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');

describe('Setting Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
      },
      params: {
        category: 'personal',
      },
    };
    mockReply = {};

    // Reset mocks
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.SETTING}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
        true,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call SettingService.index if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await index(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(SettingService.index).toHaveBeenCalledWith(mockRequest, 'personal');
    });
  });

  describe('updatePersonal', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updatePersonal(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.UPDATE_PERSONAL,
      });

      // Call the serviceFn to verify SettingService.update is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(SettingService.update).toHaveBeenCalledWith(
        mockRequest,
        SettingConstant.SETTING_CATEGORIES.PERSONAL,
      );
    });
  });

  describe('updateSafety', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateSafety(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.UPDATE_SAFETY,
      });

      // Call the serviceFn to verify SettingService.update is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(SettingService.update).toHaveBeenCalledWith(
        mockRequest,
        SettingConstant.SETTING_CATEGORIES.SAFETY,
      );
    });
  });

  describe('updateThemes', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateThemes(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.UPDATE_THEMES,
      });

      // Call the serviceFn to verify SettingService.update is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(SettingService.update).toHaveBeenCalledWith(
        mockRequest,
        SettingConstant.SETTING_CATEGORIES.THEMES,
      );
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });

      // Call the serviceFn to verify SettingService.list is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(SettingService.list).toHaveBeenCalledWith(mockRequest);
    });
  });
});
