import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as settingRepository from '#src/modules/setting/repository/setting.repository.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/query.util.js');

describe('Setting Repository', () => {
  let mockServer = {};
  const mockWhereFilter = { mock: 'where' };
  const mockIncludeFilter = [{ mock: 'include' }];

  beforeEach(() => {
    mockServer = {
      psql: {
        Setting: {
          scope: vi.fn().mockReturnThis(),
          findAll: vi.fn(),
        },
        CustomSetting: {
          scope: vi.fn().mockReturnThis(),
          findOrCreate: vi.fn(),
          bulkCreate: vi.fn(),
        },
      },
    };

    buildWhereFromFilters.mockReturnValue({
      where: mockWhereFilter,
      include: mockIncludeFilter,
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findByCategoryAndAccess', () => {
    it('should call Setting.findAll with correct parameters', async () => {
      const entityId = '123';
      const category = 'testCategory';
      const accessLevel = 'user';

      const mockSettings = [
        { toSafeObject: vi.fn(() => ({ id: '1', name: 'Setting 1' })) },
        { toSafeObject: vi.fn(() => ({ id: '2', name: 'Setting 2' })) },
      ];

      mockServer.psql.Setting.findAll.mockResolvedValue(mockSettings);

      const query = {
        'filter_customSettings.entityId_eq': entityId,
        filter_category_eq: category,
        filter_accessLevels_overlap: accessLevel,
      };

      const result = await settingRepository.findByCategoryAndAccess(mockServer, query);

      expect(mockServer.psql.Setting.findAll).toHaveBeenCalledWith({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });

      expect(result).toEqual([
        { id: '1', name: 'Setting 1' },
        { id: '2', name: 'Setting 2' },
      ]);
    });
  });

  describe('upsertCustomSettings', () => {
    it('should upsert multiple custom settings', async () => {
      const updateData = [
        { entityId: '123', parentId: '1', value: 'value1', version: 1 },
        { entityId: '123', parentId: '2', value: 'value2', version: 2 },
      ];

      const mockCustomSetting = {
        update: vi.fn(),
      };

      mockServer.psql.CustomSetting.findOrCreate
        .mockResolvedValueOnce([mockCustomSetting, true])
        .mockResolvedValueOnce([mockCustomSetting, false]);

      await settingRepository.upsertCustomSettings(mockServer, updateData);

      expect(mockServer.psql.CustomSetting.findOrCreate).toHaveBeenCalledTimes(2);
      expect(mockCustomSetting.update).toHaveBeenCalledTimes(1);

      // Check if findOrCreate was called with the correct arguments for each item
      updateData.forEach((data, index) => {
        expect(mockServer.psql.CustomSetting.findOrCreate).toHaveBeenNthCalledWith(index + 1, {
          where: {
            parentId: data.parentId,
            entityId: data.entityId,
          },
          defaults: {
            value: data.value,
            version: data.version,
          },
        });
      });

      // Check if update was called for the second item (not created)
      expect(mockCustomSetting.update).toHaveBeenCalledWith({ value: 'value2', version: 2 }, {});
    });

    it('should pass options to database operations', async () => {
      const updateData = [{ entityId: '123', parentId: '1', value: 'value1', version: 1 }];
      const options = { transaction: 'mockTransaction' };

      const mockCustomSetting = {
        update: vi.fn(),
      };

      mockServer.psql.CustomSetting.findOrCreate.mockResolvedValueOnce([mockCustomSetting, false]);

      await settingRepository.upsertCustomSettings(mockServer, updateData, options);

      expect(mockServer.psql.CustomSetting.findOrCreate).toHaveBeenCalledWith(
        expect.objectContaining({
          ...options,
          where: expect.any(Object),
          defaults: expect.any(Object),
        }),
      );
      expect(mockCustomSetting.update).toHaveBeenCalledWith(
        { value: 'value1', version: 1 },
        options,
      );
    });
  });

  describe('bulkCreate', () => {
    it('should run correctly without options', async () => {
      const data = [{ entityId: '123', parentId: '1', value: 'value1', localisationId: 'uuid' }];

      await settingRepository.bulkCreate(mockServer, data);

      expect(mockServer.psql.CustomSetting.bulkCreate).toHaveBeenCalledWith(data, {});
    });

    it('should pass options to database operations', async () => {
      const data = [{ entityId: '123', parentId: '1', value: 'value1', localisationId: 'uuid' }];
      const options = { transaction: 'mockTransaction' };

      await settingRepository.bulkCreate(mockServer, data, options);

      expect(mockServer.psql.CustomSetting.bulkCreate).toHaveBeenCalledWith(
        data,
        expect.objectContaining({
          ...options,
        }),
      );
    });
  });
});
