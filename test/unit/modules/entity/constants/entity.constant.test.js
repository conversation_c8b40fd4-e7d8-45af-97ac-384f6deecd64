import { describe, expect, it } from 'vitest';
import { EntityConstant } from '#src/modules/entity/index.js';

describe('Entity Constants', () => {
  it('should have the correct value for MERCHANT_SETTING_DEFAULT', () => {
    // expect shared settings default to be inherited from organisation
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.language).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.timezone).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.colorScheme).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.twoFactorEnforcement).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.passwordMinimumLength).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.passwordRequiredUppercase).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.passwordRequiredLowercase).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.passwordRequiredNumber).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.passwordRequiredSpecial).toEqual('');
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.accessControl).toEqual('');

    // expect merchant only settings to be defined
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.usernameMasking).toEqual(false);
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.emailMasking).toEqual(0);
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.mobileMasking).toEqual(3);
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.mobileMaskingStart).toEqual(6);
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.mobileMaskingEnd).toEqual(6);
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.lowCreditThreshold1).toEqual(5000);
    expect(EntityConstant.MERCHANT_SETTING_DEFAULT.lowCreditThreshold2).toEqual(1000);

    expect(Object.keys(EntityConstant.MERCHANT_SETTING_DEFAULT)).toHaveLength(17);
  });
});
