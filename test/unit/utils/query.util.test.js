import { DataTypes, Op } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  buildWhereFromFilters,
  exportForUnitTest,
  prepareScopes,
  prepareSortOrder,
} from '#src/utils/query.util.js';

const {
  adaptValidationError,
  buildIncludeOptions,
  checkAssociateValid,
  checkAttributeValid,
  createSchemaFromAttribute,
  mergeIncludeFilters,
} = exportForUnitTest;

// create mock models
const mockWalletModel = {
  name: 'wallet',
  rawAttributes: {
    id: { type: new DataTypes.UUID() },
    balance: { type: new DataTypes.DECIMAL() },
  },
};

const mockMemberModel = {
  name: 'member',
  rawAttributes: {
    id: { type: new DataTypes.UUID() },
    name: { type: new DataTypes.STRING() },
  },
  associations: {
    wallet: {
      target: mockWalletModel,
    },
  },
};

const mockEntityModel = {
  name: 'entity',
  rawAttributes: {
    id: { type: new DataTypes.UUID() },
    name: { type: new DataTypes.STRING() },
    accessLevels: { type: new DataTypes.ARRAY(DataTypes.STRING) },
    status: { type: new DataTypes.ENUM('active', 'inactive') },
  },
  associations: {
    member: {
      target: mockMemberModel,
    },
  },
};

describe('query.util.js', () => {
  describe('prepareScopes', () => {
    it('should return an empty array for empty filters', () => {
      expect(prepareScopes({})).toEqual([]);
    });

    it('should ignore undefined and null values', () => {
      const filters = { filter_name: 'test', filter_age: undefined, filter_city: null };
      expect(prepareScopes(filters)).toEqual([{ method: ['byName', 'test'] }]);
    });

    it('should create correct scope methods', () => {
      const filters = { filter_name: 'John', filter_age: 30 };
      expect(prepareScopes(filters)).toEqual([
        { method: ['byName', 'John'] },
        { method: ['byAge', 30] },
      ]);
    });

    it('should ignore keys that do not follow the filter_ pattern', () => {
      const filters = { filter_name: 'John', invalidKey: 'value' };
      expect(prepareScopes(filters)).toEqual([{ method: ['byName', 'John'] }, {}]);
    });
  });

  describe('prepareSortOrder', () => {
    let mockFastify;

    beforeEach(() => {
      mockFastify = {
        psql: {
          User: {},
          Post: {},
        },
      };
    });

    it('should return default sort order when sortBy is empty', () => {
      expect(prepareSortOrder(mockFastify, [])).toEqual([['id', 'DESC']]);
      expect(prepareSortOrder(mockFastify, null)).toEqual([['id', 'DESC']]);
      expect(prepareSortOrder(mockFastify, undefined)).toEqual([['id', 'DESC']]);
    });

    it('should handle single string sortBy', () => {
      expect(prepareSortOrder(mockFastify, 'name:asc')).toEqual([['name', 'ASC']]);
    });

    it('should handle array of sortBy', () => {
      expect(prepareSortOrder(mockFastify, ['name:asc', 'age:desc'])).toEqual([
        ['name', 'ASC'],
        ['age', 'DESC'],
      ]);
    });

    it('should handle association sorting', () => {
      const result = prepareSortOrder(mockFastify, ['user.name:asc']);
      expect(result).toEqual([[{ model: mockFastify.psql.User, as: 'user' }, 'name', 'ASC']]);
    });

    it('should throw error for unknown association', () => {
      expect(() => prepareSortOrder(mockFastify, ['unknown.name:asc'])).toThrow(
        'Association model Unknown not found in fastify.psql',
      );
    });

    it('should filter out falsy sortBy items', () => {
      expect(prepareSortOrder(mockFastify, ['name:asc', null, 'age:desc', undefined])).toEqual([
        ['name', 'ASC'],
        ['age', 'DESC'],
      ]);
    });

    it('should handle mixed simple and association sorting', () => {
      const result = prepareSortOrder(mockFastify, ['name:asc', 'user.age:desc']);
      expect(result).toEqual([
        ['name', 'ASC'],
        [{ model: mockFastify.psql.User, as: 'user' }, 'age', 'DESC'],
      ]);
    });
  });

  describe('createSchemaFromAttribute', () => {
    it('should create boolean schema for BOOLEAN type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.BOOLEAN());

      expect(schema).toEqual({ type: 'boolean' });
    });

    it('should create integer schema for INTEGER type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.INTEGER());

      expect(schema).toEqual({ type: 'integer' });
    });

    it('should create integer schema for BIGINT type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.BIGINT());

      expect(schema).toEqual({ type: 'integer' });
    });

    it('should create object schema for JSON type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.JSON());

      expect(schema).toEqual({ type: 'object' });
    });

    it('should create object schema for JSONB type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.JSONB());

      expect(schema).toEqual({ type: 'object' });
    });

    it('should create number schema for FLOAT type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.FLOAT());

      expect(schema).toEqual({ type: 'number' });
    });

    it('should create number schema for DECIMAL type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.DECIMAL());

      expect(schema).toEqual({ type: 'number' });
    });

    it('should create string schema for STRING type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.STRING());

      expect(schema).toEqual({ type: 'string' });
    });

    it('should create string schema for TEXT type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.TEXT());

      expect(schema).toEqual({ type: 'string' });
    });

    it('should create enum schema for ENUM type', () => {
      const values = ['active', 'inactive'];
      const enumType = new DataTypes.ENUM(values);
      const schema = createSchemaFromAttribute(enumType);

      expect(schema).toEqual({
        type: 'string',
        enum: values,
      });
    });

    it('should create uuid schema for UUID type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.UUID());

      expect(schema).toEqual({ type: 'string', format: 'uuid' });
    });

    it('should create date-time schema for DATE type', () => {
      const schema = createSchemaFromAttribute(new DataTypes.DATE());

      expect(schema).toEqual({ type: 'string', format: 'date-time' });
    });

    it('should create array schema for ARRAY type', () => {
      const arrayType = new DataTypes.ARRAY(new DataTypes.STRING());
      const schema = createSchemaFromAttribute(arrayType);

      expect(schema).toEqual({
        type: 'array',
        items: { type: 'string' },
      });
    });

    it('should create nested array schema for ARRAY of ARRAY type', () => {
      const nestedArrayType = new DataTypes.ARRAY(new DataTypes.ARRAY(new DataTypes.INTEGER()));
      const schema = createSchemaFromAttribute(nestedArrayType);

      expect(schema).toEqual({
        type: 'array',
        items: {
          type: 'array',
          items: { type: 'integer' },
        },
      });
    });

    it('should return empty object for unknown type', () => {
      const schema = createSchemaFromAttribute({});

      expect(schema).toEqual({});
    });
  });

  describe('adaptValidationError', () => {
    const validationError = [
      {
        instancePath: [],
        schemaPath: '',
        keyword: '',
        params: {},
        message: 'a required field',
      },
    ];
    const key = 'name';

    it('should return as an array', () => {
      const result = adaptValidationError(validationError, key);

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toEqual(1);
    });

    it('should modify instancePath and put key to position 2 of array', () => {
      const result = adaptValidationError(validationError, key);

      expect(result[0].instancePath.length).toEqual(2);
      expect(result[0].instancePath[1]).toEqual(key);
    });

    it('should adapt non validation error to validation error', () => {
      const nonValidationError = [
        {
          message: 'non validation error message',
        },
      ];

      const result = adaptValidationError(nonValidationError, key);

      expect(result).toEqual([
        {
          instancePath: ['', key],
          schemaPath: '',
          keyword: '',
          params: {},
          message: nonValidationError[0].message,
        },
      ]);
    });
  });

  describe('checkAssociateValid', () => {
    it('should return false if association not found', () => {
      const result = checkAssociateValid(mockEntityModel, 'Invalid');

      expect(result).toStrictEqual(false);
    });

    it('should return associate model when found', () => {
      const result = checkAssociateValid(mockEntityModel, mockMemberModel.name);

      expect(result).toStrictEqual(mockMemberModel);
    });
  });

  describe('checkAttributeValid', () => {
    it('should return error when attribute not found', () => {
      const result = checkAttributeValid(mockEntityModel, 'invalid', 'query');

      expect(result).toStrictEqual({
        error: [{ message: 'has invalid attribute' }],
      });
    });

    it('should return valid if attribute found and query value is valid', () => {
      const result = checkAttributeValid(mockEntityModel, 'name', 'John');

      expect(result).toStrictEqual({ valid: true });
    });

    it('should able to handle array value of query', () => {
      const result = checkAttributeValid(mockEntityModel, 'name', ['John', 'Doe']);

      expect(result).toStrictEqual({ valid: true });
    });

    it('should able to handle array type attribute', () => {
      const result = checkAttributeValid(mockEntityModel, 'accessLevels', 'root');

      expect(result).toStrictEqual({ valid: true });
    });

    it('should return error when query value is invalid', () => {
      const result = checkAttributeValid(mockEntityModel, 'id', 'invalid uuid');

      expect(result).toStrictEqual({ error: expect.anything() });
      // used anything as what is inside the error is not in the scope of this assert
    });
  });

  describe('buildIncludeOptions', () => {
    it('should build simple include options for single level association', () => {
      const includes = [];
      const levels = ['member'];
      const where = { name: { [Op.eq]: 'John' } };

      buildIncludeOptions(includes, levels, where);

      expect(includes).toEqual([
        {
          association: 'member',
          where: { name: { [Op.eq]: 'John' } },
        },
      ]);
    });

    it('should build nested include options for multi-level associations', () => {
      const includes = [];
      const levels = ['member', 'wallet'];
      const where = { balance: { [Op.gt]: 100 } };

      buildIncludeOptions(includes, levels, where);

      expect(includes).toEqual([
        {
          association: 'member',
          include: [
            {
              association: 'wallet',
              where: { balance: { [Op.gt]: 100 } },
            },
          ],
        },
      ]);
    });

    it('should merge where conditions when adding to existing association', () => {
      const includes = [
        {
          association: 'member',
          where: { status: { [Op.eq]: 'active' } },
        },
      ];
      const levels = ['member'];
      const where = { name: { [Op.eq]: 'John' } };

      buildIncludeOptions(includes, levels, where);

      expect(includes).toEqual([
        {
          association: 'member',
          where: {
            status: { [Op.eq]: 'active' },
            name: { [Op.eq]: 'John' },
          },
        },
      ]);
    });

    it('should add to existing nested structure without duplicating associations', () => {
      const includes = [
        {
          association: 'member',
          include: [
            {
              association: 'bankAccount',
              where: { verified: { [Op.eq]: true } },
            },
          ],
        },
      ];
      const levels = ['member', 'wallet'];
      const where = { balance: { [Op.gt]: 100 } };

      buildIncludeOptions(includes, levels, where);

      expect(includes).toEqual([
        {
          association: 'member',
          include: [
            {
              association: 'bankAccount',
              where: { verified: { [Op.eq]: true } },
            },
            {
              association: 'wallet',
              where: { balance: { [Op.gt]: 100 } },
            },
          ],
        },
      ]);
    });
  });

  describe('buildWhereFromFilters', () => {
    let mockInclude = [];

    beforeEach(() => {
      vi.resetAllMocks();

      mockInclude = [
        {
          association: 'member',
          include: [
            {
              association: 'wallet',
            },
          ],
        },
      ];

      vi.spyOn(exportForUnitTest, 'adaptValidationError').mockReturnValue([]);
    });

    it('should build where conditions for direct field filters', () => {
      const filters = {
        filter_name_eq: 'Test',
        filter_status_eq: 'active',
      };

      const result = buildWhereFromFilters(filters, mockEntityModel);

      expect(result).toEqual({
        where: {
          name: { [Op.eq]: 'Test' },
          status: { [Op.eq]: 'active' },
        },
        include: [],
      });
    });

    it('should build include options for association filters', () => {
      const filters = {
        'filter_member.wallet.balance_gt': '100',
        'filter_member.name_eq': 'John',
      };

      const result = buildWhereFromFilters(filters, mockEntityModel, mockInclude);

      expect(result).toEqual({
        where: {},
        include: [
          {
            association: 'member',
            where: { name: { [Op.eq]: 'John' } },
            include: [
              {
                association: 'wallet',
                where: { balance: { [Op.gt]: '100' } },
              },
            ],
          },
        ],
      });
    });

    it('should handle combination of direct and association filters', () => {
      const filters = {
        filter_name_eq: 'John',
        'filter_member.wallet.balance_gt': '100',
      };

      const result = buildWhereFromFilters(filters, mockEntityModel, mockInclude);

      expect(result).toEqual({
        where: {
          name: { [Op.eq]: 'John' },
        },
        include: [
          {
            association: 'member',
            include: [
              {
                association: 'wallet',
                where: { balance: { [Op.gt]: '100' } },
              },
            ],
          },
        ],
      });
    });

    it('should able to handle array type operators', () => {
      const filters = {
        filter_status_in: 'active,inactive',
      };

      const result = buildWhereFromFilters(filters, mockEntityModel);

      expect(result.where).toEqual({
        status: { [Op.in]: ['active', 'inactive'] },
      });
    });

    it('should ignore filters with invalid format, undefined or null values', () => {
      const filters = {
        name_eq: 'John',
        filter_status_eq: undefined,
        filter_age_gte: null,
      };

      const result = buildWhereFromFilters(filters, mockEntityModel);

      expect(result.where).toEqual({});
    });

    it('should ignore filters with invalid operators', () => {
      const filters = {
        filter_name_eq: 'John',
        filter_status_active: 'active',
      };

      const result = buildWhereFromFilters(filters, mockEntityModel);

      expect(result.where).toEqual({
        name: { [Op.eq]: 'John' },
      });
    });

    it('should throw validation error for invalid attribute or query', () => {
      const filters = {
        filter_invalid_eq: 'value',
        'filter_member.invalid_eq': 'value',
      };
      const expectedError = new Error('Validation Error');

      expect(() => buildWhereFromFilters(filters, mockEntityModel)).toThrow(expectedError);
    });

    it('should ignore invalid association paths', () => {
      const filters = {
        'filter_invalid.field_eq': 'value',
        filter_name_eq: 'John',
      };

      const result = buildWhereFromFilters(filters, mockEntityModel);

      expect(result).toEqual({
        where: {
          name: { [Op.eq]: 'John' },
        },
        include: [],
      });
    });
  });

  describe('mergeIncludeFilters', () => {
    const filterIncludes = [
      {
        association: 'baseModel',
        where: { id: 1 },
        include: [
          {
            association: 'nestedModel',
            where: { id: 2 },
            include: [
              {
                association: 'deeperModel',
                where: { id: 3 },
              },
            ],
          },
        ],
      },
      { association: 'customModel', where: { name: 'test' } },
    ];
    const expectResult = [
      {
        association: 'baseModel',
        required: true,
        where: { id: 1 },
        include: [
          {
            association: 'nestedModel',
            required: true,
            where: { id: 2 },
            include: [
              {
                association: 'deeperModel',
                required: true,
                where: { id: 3 },
              },
            ],
          },
        ],
      },
      { association: 'customModel', required: true, where: { name: 'test' } },
    ];
    let baseIncludes = [];

    beforeEach(() => {
      vi.clearAllMocks();

      baseIncludes = [
        {
          association: 'baseModel',
          required: true,
          include: [
            {
              association: 'nestedModel',
              required: true,
              include: [
                {
                  association: 'deeperModel',
                  required: true,
                },
              ],
            },
          ],
        },
        {
          association: 'customModel',
          required: true,
        },
      ];
    });

    it('should merge where filters recursively when association is found', () => {
      const result = mergeIncludeFilters(baseIncludes, filterIncludes);

      expect(result).toEqual(expectResult);
    });

    it('should not merge where filters when association is not found', () => {
      const result = mergeIncludeFilters(baseIncludes, []);

      expect(result).toEqual(baseIncludes);
    });

    it('should not merge nested where filters when association is not found', () => {
      const mockIncludes = [
        {
          association: 'baseModel',
          where: { id: 1 },
          include: [
            {
              association: 'invalidModel',
              where: { id: 2 },
            },
          ],
        },
      ];
      const result = mergeIncludeFilters(baseIncludes, mockIncludes);

      expect(result).toEqual(baseIncludes);
    });
  });
});
