import { beforeEach, describe, expect, it, vi } from 'vitest';
import applyOffsetPagination from '#src/utils/offset-pagination.util.js';

describe('applyOffsetPagination', () => {
  const mockFastify = {
    // Mock any Fastify utilities used in prepareSortOrder
  };

  const mockModel = {
    unscoped: vi.fn().mockReturnThis(),
    scope: vi.fn().mockReturnThis(),
    findAndCountAll: vi.fn(),
  };

  const mockIncludes = [{ model: 'relatedModel' }];

  beforeEach(() => {
    vi.clearAllMocks();

    mockModel.unscoped.mockReturnValue(mockModel);

    mockModel.findAndCountAll.mockResolvedValue({
      count: 100,
      rows: Array(10).fill({ id: 1 }),
    });
  });

  it('should apply default pagination when no parameters are provided', async () => {
    const result = await applyOffsetPagination(mockFastify, mockModel, {}, {});

    expect(mockModel.findAndCountAll).toHaveBeenCalledWith({
      limit: 25,
      offset: 0,
      order: expect.anything(),
      where: {},
    });

    expect(result).toEqual({
      rows: expect.any(Array),
      pagination: {
        totalCount: 100,
        totalPages: 4,
        currentPage: 1,
        limit: 25,
      },
    });
  });

  it('should apply custom pagination parameters', async () => {
    const result = await applyOffsetPagination(
      mockFastify,
      mockModel,
      {
        page: 2,
        limit: 10,
      },
      {},
    );

    expect(mockModel.findAndCountAll).toHaveBeenCalledWith({
      limit: 10,
      offset: 10,
      order: expect.anything(),
      where: {},
    });

    expect(result.pagination).toEqual({
      totalCount: 100,
      totalPages: 10,
      currentPage: 2,
      limit: 10,
    });
  });

  it('should include related models when includes are provided', async () => {
    await applyOffsetPagination(mockFastify, mockModel, {}, {}, mockIncludes);

    expect(mockModel.findAndCountAll).toHaveBeenCalledWith({
      limit: 25,
      offset: 0,
      order: expect.anything(),
      where: {},
      include: mockIncludes,
    });
  });

  it('should handle zero results', async () => {
    mockModel.findAndCountAll.mockResolvedValueOnce({
      count: 0,
      rows: [],
    });

    const result = await applyOffsetPagination(mockFastify, mockModel, {});

    expect(result.pagination).toEqual({
      totalCount: 0,
      totalPages: 0,
      currentPage: 1,
      limit: 25,
    });
  });

  it('should handle no pagination request', async () => {
    const mockCount = 1;
    const mockRows = ['hi'];
    const mockLimit = 0;
    mockModel.findAndCountAll.mockResolvedValueOnce({
      count: mockCount,
      rows: mockRows,
    });

    const result = await applyOffsetPagination(mockFastify, mockModel, { limit: mockLimit });

    const [queryOptions] = mockModel.findAndCountAll.mock.calls[0];
    const { limit, offset } = queryOptions;

    // Should not call with limit and offset
    expect(limit).toBeUndefined();
    expect(offset).toBeUndefined();

    expect(result.rows).toEqual(mockRows);

    // Pages should be 1 and limit 0
    expect(result.pagination.totalPages).toEqual(1);
    expect(result.pagination.currentPage).toEqual(1);
    expect(result.pagination.limit).toEqual(mockLimit);
  });
});
