import { Queue, QueueEvents } from 'bullmq';
import { baseConfig, queueNames } from '#config/queue.config.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import IORedis from 'ioredis';
import queueEventPlugin from '#src/plugins/queue-event.plugin.js';

vi.mock('ioredis');
vi.mock('bullmq');

describe('Queue event plugin', () => {
  let fastify;
  let mockQueues = {};
  let mockQueueEvents = {};
  let eventHandlers = {};

  beforeEach(() => {
    vi.clearAllMocks();
    mockQueues = {};
    mockQueueEvents = {};
    eventHandlers = {};

    queueNames.forEach((queueName) => {
      const mockQueue = {
        getJob: vi.fn().mockResolvedValue({
          processedOn: new Date(),
          finishedOn: new Date(),
          data: { jobUuid: 'test-uuid' },
        }),
      };
      mockQueues[queueName] = mockQueue;
    });

    Queue.mockImplementation((queueName) => {
      return mockQueues[queueName];
    });

    queueNames.forEach((queueName) => {
      const mockQueueEvent = {
        on: vi.fn((eventName, handler) => {
          if (!eventHandlers[queueName]) {
            eventHandlers[queueName] = {};
          }
          eventHandlers[queueName][eventName] = handler;
        }),
      };
      mockQueueEvents[queueName] = mockQueueEvent;
    });

    QueueEvents.mockImplementation((queueName) => {
      return mockQueueEvents[queueName];
    });

    fastify = Fastify();
  });

  it('should register the plugin successfully', async () => {
    await expect(fastify.register(queueEventPlugin)).resolves.not.toThrow();
  });

  it('should initiate Redis connection with base config with max retries null', async () => {
    const config = { ...baseConfig, maxRetriesPerRequest: null };
    await fastify.register(queueEventPlugin);

    expect(IORedis).toHaveBeenCalledWith(config);
  });

  it('should create Queue and QueueEvents instances for each queues', async () => {
    await fastify.register(queueEventPlugin);

    expect(Queue).toHaveBeenCalledTimes(queueNames.length);
    expect(QueueEvents).toHaveBeenCalledTimes(queueNames.length);

    queueNames.forEach((queueName) => {
      expect(Queue).toHaveBeenCalledWith(queueName, {
        connection: expect.any(IORedis),
      });

      expect(QueueEvents).toHaveBeenCalledWith(queueName, {
        connection: expect.any(IORedis),
      });
    });
  });

  it('should register event handlers for active, completed, and failed events', async () => {
    await fastify.register(queueEventPlugin);

    queueNames.forEach((queueName) => {
      const mockQueueEvent = mockQueueEvents[queueName];

      expect(mockQueueEvent.on).toHaveBeenCalledTimes(3);
      expect(mockQueueEvent.on).toHaveBeenCalledWith('active', expect.any(Function));
      expect(mockQueueEvent.on).toHaveBeenCalledWith('completed', expect.any(Function));
      expect(mockQueueEvent.on).toHaveBeenCalledWith('failed', expect.any(Function));
    });
  });

  it('should handle active event', async () => {
    await fastify.register(queueEventPlugin);

    const queueName = queueNames[0];
    const jobId = 'test-job-id';
    const mockQueue = mockQueues[queueName];

    // Trigger active event handler
    await eventHandlers[queueName].active({ jobId, prev: 'waiting' });

    expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);

    // pending further assertion
  });

  it('should handle completed event', async () => {
    await fastify.register(queueEventPlugin);

    const queueName = queueNames[0];
    const jobId = 'test-job-id';
    const mockQueue = mockQueues[queueName];

    // Trigger completed event handler
    await eventHandlers[queueName].completed({ jobId, returnvalue: 'true' });

    expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);

    // pending further assertion
  });

  it('should handle failed event', async () => {
    await fastify.register(queueEventPlugin);

    const queueName = queueNames[0];
    const jobId = 'test-job-id';
    const mockQueue = mockQueues[queueName];

    // Trigger failed event handler
    await eventHandlers[queueName].failed({ jobId, failedReason: 'Error message' });

    expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);

    // pending further assertion
  });
});
