import { baseConfig, completeRetention, failRetention, queueNames } from '#config/queue.config.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import IORedis from 'ioredis';
import { Queue } from 'bullmq';
import queuePlugin from '#src/plugins/queue.plugin.js';

vi.mock('ioredis');
vi.mock('bullmq');

describe('Queue plugin', () => {
  let fastify;
  let mockQueues = {};

  beforeEach(() => {
    vi.clearAllMocks();

    queueNames.forEach((queueName) => {
      mockQueues[queueName] = {
        add: vi.fn().mockResolvedValue(true),
      };
    });

    Queue.mockImplementation((queueName) => {
      return mockQueues[queueName];
    });

    fastify = Fastify();
  });

  it('should register the plugin successfully', async () => {
    await fastify.register(queuePlugin);

    expect(fastify.queue).toBeDefined();
    expect(typeof fastify.queue.add).toBe('function');
  });

  it('should initiate Redis connection with base config', async () => {
    await fastify.register(queuePlugin);

    expect(IORedis).toHaveBeenCalledWith(baseConfig);
  });

  it('should register queues based on config file', async () => {
    await fastify.register(queuePlugin);

    expect(Queue).toHaveBeenCalledTimes(queueNames.length);

    queueNames.forEach((queueName) => {
      expect(Queue).toHaveBeenCalledWith(
        queueName,
        expect.objectContaining({
          connection: expect.any(IORedis),
          removeOnComplete: completeRetention,
          removeOnFail: failRetention,
        }),
      );
    });
  });

  it('should add job to queue when using add decorator', async () => {
    await fastify.register(queuePlugin);

    const queueName = queueNames[0]; // Use the first queue name
    const jobName = 'testJob';
    const params = { data: 'test' };

    const result = await fastify.queue.add(queueName, jobName, params);

    expect(result).toBe(true);

    // Check if the add method was called on the correct mock queue
    expect(mockQueues[queueName].add).toHaveBeenCalledWith(jobName, params);
    expect(mockQueues[queueName].add).toHaveBeenCalledTimes(1);
  });

  it('should return false when add job to invalid queue', async () => {
    await fastify.register(queuePlugin);

    const result = await fastify.queue.add('invalid-queue', 'testJob', {});

    expect(result).toBe(false);
  });
});
