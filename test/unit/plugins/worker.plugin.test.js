import { beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import IORedis from 'ioredis';
import { Worker } from 'bullmq';

import * as QueueConfig from '#config/queue.config.js';
import * as Workers from '#src/modules/core/workers/index.js';
import workerPlugin from '#src/plugins/worker.plugin.js';

vi.mock('ioredis');
vi.mock('bullmq');
vi.mock('#src/modules/core/workers/index.js', () => ({
  EntityWorker: {
    createMerchantJob: vi.fn(),
    checkCreditLimitThreshold: vi.fn(),
  },
}));

describe('Worker plugin', () => {
  let fastify;
  let mockFastify;
  let mockWorkerInstances = {};
  let workerProcessFunctions = {};

  beforeEach(() => {
    vi.clearAllMocks();
    mockWorkerInstances = {};
    workerProcessFunctions = {};
    mockFastify = {
      register: vi.fn(),
      decorate: vi.fn(),
    };

    Worker.mockImplementation((queueName, processFn, options) => {
      const mockWorker = {
        queueName,
        processFn,
        options,
        on: vi.fn(),
        close: vi.fn().mockResolvedValue(undefined),
      };

      mockWorkerInstances[queueName] = mockWorker;
      workerProcessFunctions[queueName] = processFn;

      return mockWorker;
    });

    fastify = Fastify();
  });

  it('should register the plugin successfully', async () => {
    await fastify.register(workerPlugin);

    expect(fastify.workers).toBeDefined();
    // expect(fastify.workers.generalWorker).toBeDefined();
    expect(fastify.workers.entityWorker).toBeDefined();

    // expect(fastify.workers.generalWorker).toBe(mockWorkerInstances['general-queue']);
    expect(fastify.workers.entityWorker).toBe(mockWorkerInstances['entity-queue']);
  });

  it('should initiate Redis connection with base config with max retries null', async () => {
    const config = { ...QueueConfig.baseConfig, maxRetriesPerRequest: null };

    await workerPlugin(mockFastify);

    expect(IORedis).toHaveBeenCalledWith(config);
  });

  it('should create Worker instances for all queues', async () => {
    await workerPlugin(mockFastify);

    expect(Worker).toHaveBeenCalledTimes(1);

    for (const queueName of QueueConfig.queueNames) {
      expect(Worker).toHaveBeenCalledWith(queueName, expect.any(Function), {
        connection: expect.any(IORedis),
        concurrency: QueueConfig.workerConcurrency,
      });
    }
  });

  it('should handle entity-queue job correctly', async () => {
    await workerPlugin(mockFastify);

    const job = { name: 'create', data: { test: 'data' } };
    await workerProcessFunctions['entity-queue'](job);

    expect(Workers.EntityWorker.createMerchantJob).toHaveBeenCalledWith(mockFastify, job.data);

    const job2 = { name: 'check-balance', data: { test: 'data' } };
    await workerProcessFunctions['entity-queue'](job2);

    expect(Workers.EntityWorker.checkCreditLimitThreshold).toHaveBeenCalledWith(
      mockFastify,
      job2.data,
    );
  });

  // it('should handle general-queue job correctly', async () => {
  //   await workerPlugin(mockFastify);

  //   const job = { name: 'general-job', data: { test: 'data' } };
  //   await workerProcessFunctions['general-queue'](job);
  //   expect(true).toBeTruthy(); // Placeholder for actual implementation, to pass unit test

  //   const job2 = { name: 'general-job2', data: { test: 'data' } };
  //   await workerProcessFunctions['general-queue'](job2);
  //   expect(true).toBeTruthy(); // Placeholder for actual implementation, to pass unit test
  // });
});
